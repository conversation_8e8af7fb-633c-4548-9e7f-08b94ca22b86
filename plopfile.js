module.exports = (plop) => {
  //	参数1：生成器名称  参数2：配置项
  plop.setGenerator("component", {
    description: "create a component",
    prompts: [
      {
        type: "input",
        name: "basePath",
        message: "文件根目录(views/?/)",
        default: "MyComponent",
      },
      {
        type: "input",
        name: "path",
        message: "文件目录(views/{{basePath}}/?)",
        default: "MyComponent",
      },
    ],
    actions: [

      /* 生成的指定目录下 index.vue 页面 */

      {
        type: "add",
        path: "src/views/{{basePath}}/{{path}}/index.vue",
        templateFile: "plop-templates/templates_index.hbs",
      },

      /* 生成的指定目录下 xxx.data.ts 页面 */

      {
        type: "add",
        path: "src/views/{{basePath}}/{{path}}/{{path}}.data.ts",
        templateFile: "plop-templates/templates_data.hbs",
      },

      /* 生成的指定目录下 api 文件 */

      {
        type: "add",
        path: "src/api/{{basePath}}/{{path}}/index.ts",
        templateFile: "plop-templates/templates_api.hbs",
      },
    ],
  });
};


