<template>
  <ContentWrap>
    <Search :schema="searchSchema" @reset="setSearchParams" @search="setSearchParams"/>
  </ContentWrap>
  <ContentWrap>
    <Table
        class="mt-3"
        border
        :loading
        :columns="tableColumns"
        :data="tableObject.tableList"
        v-model:currentPage="tableObject.currentPage"
        v-model:pageSize="tableObject.pageSize"
        :pagination="{ total: tableObject.total }"
    />
  </ContentWrap>
</template>

<script setup lang="ts">
  import * as Api from "@/api/{{basePath}}/{{path}}/index";
  import {searchSchema,tableColumns} from "./{{path}}.data"
  const loading = ref(true)

  const {tableObject, tableMethods} = useTable({
    getListApi: Api.getTableList
  })

  const {getList, setSearchParams} = tableMethods


  onMounted(async () => {
    loading.value = true
    try {
      await getList()
      loading.value = false
    } catch (e) {
      loading.value = false
    }
  })
</script>


<style lang="scss" scoped>
</style>


