/**
 * 数据字典工具类
 */
import { useDictStoreWithOut } from '@/store/modules/dict'
import { ElementPlusInfoType } from '@/types/elementPlus'

const dictStore = useDictStoreWithOut()

/**
 * 获取 dictType 对应的数据字典数组
 *
 * @param dictType 数据类型
 * @returns {*|Array} 数据字典数组
 */
export interface DictDataType {
  dictType: string
  label: string
  value: string | number | boolean
  colorType: ElementPlusInfoType | ''
  cssClass: string
}

export interface NumberDictDataType extends DictDataType {
  value: number
}

export const getDictOptions = (dictType: string) => {
  return dictStore.getDictByType(dictType) || []
}

export const getIntDictOptions = (dictType: string): NumberDictDataType[] => {
  // 获得通用的 DictDataType 列表
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  // 转换成 number 类型的 NumberDictDataType 类型
  // why 需要特殊转换：避免 IDEA 在 v-for="dict in getIntDictOptions(...)" 时，el-option 的 key 会告警
  const dictOption: NumberDictDataType[] = []
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: parseInt(dict.value + '')
    })
  })
  return dictOption
}

export const getStrDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + ''
    })
  })
  return dictOption
}

export const getBoolDictOptions = (dictType: string) => {
  const dictOption: DictDataType[] = []
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  dictOptions.forEach((dict: DictDataType) => {
    dictOption.push({
      ...dict,
      value: dict.value + '' === 'true'
    })
  })
  return dictOption
}

/**
 * 获取指定字典类型的指定值对应的字典对象
 * @param dictType 字典类型
 * @param value 字典值
 * @return DictDataType 字典对象
 */
export const getDictObj = (dictType: string, value: any): DictDataType | undefined => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  for (const dict of dictOptions) {
    if (dict.value === value + '') {
      return dict
    }
  }
}

/**
 * 获得字典数据的文本展示
 *
 * @param dictType 字典类型
 * @param value 字典数据的值
 * @return 字典名称
 */
export const getDictLabel = (dictType: string, value: any): string => {
  const dictOptions: DictDataType[] = getDictOptions(dictType)
  const dictLabel = ref('')
  dictOptions.forEach((dict: DictDataType) => {
    if (dict.value === value + '') {
      dictLabel.value = dict.label
    }
  })
  return dictLabel.value
}

export enum DICT_TYPE {
  USER_TYPE = 'user_type',
  COMMON_STATUS = 'common_status',
  TERMINAL = 'terminal', // 终端
  DATE_INTERVAL = 'date_interval', // 数据间隔

  // ========== SYSTEM 客户信息 模块 ==========
  SYSTEM_CUSTOMER_IMPORTANCE = 'customer_importance',
  SYSTEM_CRM_USER_SEX = 'system_crm_user_sex',
  SYSTEM_CRM_ROLE = 'system_crm_role',
  SYSTEM_CRM_CONTACT_PLAN = 'system_crm_contact_plan',
  SYSTEM_CRM_AGE_GRADES = 'system_crm_age_grades',
  SYSTEM_CRM_CUSTOMER_TYPE = 'system_crm_customer_type',
  SYSTEM_CRM_QUALITY = 'system_crm_quality',
  SYSTEM_CRM_SUPPLIER_TYPE = 'system_crm_supplier_type',
  SYSTEM_CRM_PAYMENTS_TYPE = 'system_crm_payments_type',

  // ========== SYSTEM 模块 ==========

  SYSTEM_NOTIFY_TEMPLATE_TYPE = 'system_notify_template_type',

  // ========== INFRA 模块 ==========
  INFRA_BOOLEAN_STRING = 'infra_boolean_string',

  // ========== BPM 模块 ==========
  BPM_TASK_CANDIDATE_STRATEGY = 'bpm_task_candidate_strategy',
  BPM_TASK_STATUS = 'bpm_task_status',
  BPM_PROCESS_LISTENER_TYPE = 'bpm_process_listener_type',
  BPM_PROCESS_LISTENER_VALUE_TYPE = 'bpm_process_listener_value_type',

  // ========== BMS 模块 ==========
  BMS_QUOTATION_METHOD = 'bms_quotation_method',
  BMS_DOMAIN = 'bms_domain',
  BMS_SUB_TYPE = 'bms_sub_type',
  BMS_PLATFORM = 'bms_platform',
  BMS_PURCHASER = 'bms_purchaser',
  BMS_INSTITUTION_NAME = 'bms_institution_name',
  BMS_UPLOAD_STATUS = 'bms_upload_status',
  WHETHER_TO_ACCEPT_WINNING_BID = 'whether_to_accept_winning_bid',
  BMS_IS_BID = 'bms_is_bid',
  BMS_UPLOAD_DIFFICULTY = 'bms_upload_difficulty',
  BMS_IS_PAY = 'bms_is_pay',
  BMS_BITCH_TYPE = 'bms_bitch_type',
}
