
const download0 = (data: Blob, fileName?: string, mineType?: string) => {

  // 创建 blob
  const blob = new Blob([data], { type: mineType })
  // 创建 href 超链接，点击进行下载
  window.URL = window.URL || window.webkitURL
  const href = URL.createObjectURL(blob)
  const downA: any = document.createElement('a')
  downA.href = href
  downA.download = fileName
  downA.click()
  // 销毁超连接
  window.URL.revokeObjectURL(href)
}

interface ResponseHeaders {
  'content-disposition'?: string;
  'content-type'?: string;
}

interface Response {
  headers: ResponseHeaders;
  data: any;
}

interface FileInfo {
  fileName: string;
  type?: string;
}

// 抽离的函数：获取文件名和类型
function extractFileInfo(res:Response): FileInfo | undefined{
  const disposition = res.headers['content-disposition'];
  const type = res.headers['content-type'];

  let fileName = 'default_file'; // 默认文件名
  if (!disposition) return {fileName, type};

  const match = disposition.match(/filename\*?=(?:UTF-8'')?(.+)/); // 处理 filename* 或普通 filename
  if (match && match[1]) {
    try {
      // 文件名 URL 解码，且考虑 UTF-8 编码
      fileName = decodeURIComponent(escape(atob(match[1].replace(/%/g, '%25'))));
    } catch (e) {
      console.error("Error decoding filename:", e);
      fileName = decodeURIComponent(match[1]);
    }
  }
  console.log('fileName',fileName)
  // 如果最终文件名为空，抛出错误
  // if (!fileName || fileName.trim() === '') {
  //   throw new Error('解析到的文件名为空');
  // }
  download0(res.data, fileName, type);
}

const download = {
  // 下载 Excel 方法
  excel: <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },
  // 下载 Word 方法
  word: <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },
  // 下载 Zip 方法
  zip:  <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },
  // 下载 Html 方法
  html:  <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },

  // 下载 Markdown 方法
  markdown:  <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },

  // 通用下载方法
  common: <T extends { headers: { [key: string]: string }, data: any }>(res: T) => {
    extractFileInfo(res);
  },
}

export default download

