import { floatToFixed2 } from '@/utils'

// 格式化金额【分转元】
// @ts-ignore
export const fenToYuanFormat = (_, __, cellValue: any, ___) => {
  return `￥${floatToFixed2(cellValue)}`
}
// 格式化表单数据
export const formatFormData = (res: Object, schema: Array<any>) => {
  const formData = {}
  schema.forEach((item) => {
    formData[item.field] = res[item.field]
  })
  return formData
}
//添加value label 用于回现和取值
export const addValueProperty = (node, filed: string) => {
  node.forEach((item) => {
    item.value = item.id
    item.label = item[filed]
    // 如果存在 children 项，递归处理 children 项
    if (item.children && item.children.length > 0) {
      addValueProperty(item.children, filed)
    }
  })
  return node
}
//  fullLabel 字段 用于搜索过滤 展示父级包含的子节点
export const addFullLabel = (data: any[], parentLabel: string = ''): any[] => {
  return data.map((item) => {
    // 拼接当前节点的 label
    const currentLabel = parentLabel ? `${parentLabel} > ${item.label}` : item.label
    // 如果有 children，递归处理子节点
    let children: any = []
    if (item.children) {
      children = addFullLabel(item.children, currentLabel)
    }
    // 返回一个新对象，并在其中添加 fullLabel 字段
    return {
      ...item,
      fullLabel: currentLabel, // 增加 fullLabel 字段
      children: children.length > 0 ? children : undefined // 如果有子节点则保留
    }
  })
}

export const formatDictData = (dict, value) => {
  const dictItem = dict.find((item) => item.value == value)
  return dictItem ? dictItem.label : ''
}

// 移除对象中值为 undefined、null、空字符串（可选）等“无效值”的字段。
export function cleanEmptyKeys(
  input: Record<string, any> | Record<string, any>[],
  options = { removeEmptyString: false },
  excludeFields: string[] = []
): typeof input {
  const isInvalid = (key: string, val: any): boolean => {
    if (excludeFields.includes(key)) return false
    return val === undefined || val === null || (options.removeEmptyString && val === '')
  }

  const clean = (obj: Record<string, any>) => {
    const result: Record<string, any> = {}
    for (const key in obj) {
      const value = obj[key]
      if (!isInvalid(key, value)) {
        result[key] = value
      }
    }
    return result
  }

  if (Array.isArray(input)) {
    return input.map(clean)
  }

  return clean(input)
}
