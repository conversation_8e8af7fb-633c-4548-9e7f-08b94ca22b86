import {store} from '@/store'
import {defineStore} from 'pinia'




interface UserInfoVO {
  logData: string[]
}

export const useSystemInfoStore = defineStore('admin-crm-info', {
  state: (): UserInfoVO => ({
    logData: [],
  }),
  getters: {
    getLogData(): string[] {
      return this.logData
    },

  },
  actions: {
    async setLogData(data) {
      this.logData = data
    },
    async clearLogData(){
      this.logData = []
    }
  },
})

export const useSystemInfoWithOut = () => {
  return useSystemInfoStore(store)
}
