import { defineStore } from 'pinia'
import { store } from '@/store'

export const useTenderStore = defineStore('tender-project', {
  state: () => ({
    companyList: [], // 当前项目 下参与报价的公司
    quotationMethod: null, // 报价方式
    serviceType: null, // 服务类型
    quoteInfo: {
      // 报价用到的字段 用于监听变化重新获取数据
      subDomain: null, //公司主体
      projectInfo: null // 当前的项目信息
    },
    isClose: 0, // 是否清空 store 的标志位
    isClear: false,
    projectsIndex:null,
    bidIndex:null,
    bagInfo: null
  }),

  getters: {
    getCompanyList(): any {
      return this.companyList
    },
    getQuotationMethod(): any {
      return this.quotationMethod
    },
    getServiceType(): any {
      return this.serviceType
    },
    getQuoteInfo(): any {
      return this.quoteInfo
    },
    getIsClear(): any {
      return this.isClear
    },
    getIsClose(): any {
      return this.isClose
    },
    getBagInfo(): any {
      return this.bagInfo
    },
    getProjectsIndex():number{
      return this.projectsIndex
    },
    getBidIndex():number{
      return this.bidIndex
    }
  },

  actions: {
    setProjectInfo(projectInfo) {
      this.quoteInfo.projectInfo = projectInfo
    },
    setCompanyList(list) {
      this.companyList = list
    },
    setQuotationMethod(method) {
      this.quotationMethod = method
    },
    setSubDomain(subDomain) {
      this.quoteInfo.subDomain = subDomain
    },
    setServiceType(val) {
      this.serviceType = val
    },
    setIsClose() {
      this.isClose++
    },
    setBidIndex(index:number){
      this.bidIndex = index
    },
    setProjectsIndex(index:number) {
      this.projectsIndex = index
    },
    setBagInfo(bagInfo) {
      this.bagInfo = bagInfo
    },

    //  清空 store 的方法
    resetStore() {
      this.companyList = []
      this.quotationMethod = null
      this.serviceType = null
      this.isClear = true
      ;(this.quoteInfo = {
        subDomain: null,
        projectInfo: null
      }),
        setTimeout(() => {
          this.isClear = false
        }, 1000)
    }
  }
})

export const useTenderStoreWithOut = () => {
  return useTenderStore(store)
}
