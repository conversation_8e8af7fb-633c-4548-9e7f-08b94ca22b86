import { defineStore } from 'pinia'
import { store } from '../../index'
import { useCache } from '@/hooks/web/useCache'
import * as Api from '@/api/Bms/tender/index'
interface OptionItem {
  label: string
  value: string
}
const { wsCache } = useCache('sessionStorage')

export interface OptionState {
  tList: OptionItem[]
  uList: OptionItem[]
  bList: OptionItem[]
  isLoaded: boolean
}

export const useOptionStore = defineStore('option', {
  state: (): OptionState => ({
    tList: [],
    uList: [],
    bList: [],
    isLoaded: false
  }),
  getters: {
    getTList: (state) => state.tList,
    getUList: (state) => state.uList,
    getBList: (state) => state.bList,
    getIsLoaded: (state) => state.isLoaded
  },
  actions: {
    async fetchOptions() {
      const cache = wsCache.get('bms-option')
      if (cache) {
        this.tList = cache.tList
        this.uList = cache.uList
        this.bList = cache.bList
        this.isLoaded = true
        return
      }
      // 模拟接口返回数据
      //   const data = {
      //     blist: ['B1', 'B2', 'B3', 'B4'],
      //     tlist: ['T1', 'T2', 'T3'],
      //     ulist: ['U1', 'U2', 'U3', 'U4', 'U5']
      //   }
      //   const { blist, tlist, ulist } = data
      const data = await Api.getOptionsData()
      const { blist, tlist, ulist } = data

      const toOptions = (list: (string | null)[]) => {
        if (!Array.isArray(list)) return []
        return list.filter(Boolean).map((item) => ({ label: item!, value: item! }))
      }

      this.bList = toOptions(blist)
      this.tList = toOptions(tlist)
      this.uList = toOptions(ulist)
      this.isLoaded = true

      wsCache.set(
        'bms-option',
        {
          tList: this.tList,
          uList: this.uList,
          bList: this.bList
        },
        { exp: 60 * 10 }
      ) // 10 分钟
    },

    async resetOptions() {
      wsCache.delete('bms-option')
      this.isLoaded = false
      await this.fetchOptions()
    }
  }
})

export const useOptionStoreWithOut = () => useOptionStore(store)
