<template>
  <div>
    <el-skeleton :rows="5" animated v-if="loading" />
  </div>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from 'vue'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
// 倒入路由跳转
import { useRouter } from 'vue-router'
import * as authUtil from '@/utils/auth'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { useUserStoreWithOut } from '@/store/modules/user'
import { routeRegExp } from '@/utils/regExp'
import { ElNotification } from 'element-plus'

defineOptions({ name: 'Oauth2Redirect' })
const loading = ref(true)
const router = useRouter()
const { currentRoute } = useRouter()
const permissionStore = usePermissionStoreWithOut()
const userStore = useUserStoreWithOut()

const defaultRouter = computed(() => userStore.defaultRouter)
type resRokenRaw = any
interface routeHashParam {
  access_token: string
  token_type: string
  state: string
  expires_in: number
  client_id: string
  source_url: string
}

const queryParams = reactive<routeHashParam>({
  access_token: '',
  token_type: '',
  state: '',
  expires_in: 0,
  client_id: '',
  source_url: ''
})
/** 初始化授权信息 */
const init = async (route: RouteLocationNormalizedLoaded) => {
  let match: any
  while ((match = routeRegExp.exec(route.hash)) !== null) {
    queryParams[match[1]] = match[2]
  }
  permissionStore.setSourceUrl(atob(queryParams?.source_url))
  // 路由参数携带 access_token token_type state expires_in client_id
  const resToken: resRokenRaw = {
    id: 0,
    accessToken: queryParams?.access_token, // 访问令牌
    userId: 1, // 用户编号
    userType: 1, //用户类型
    clientId: queryParams?.client_id, //客户端编号
    expiresTime: queryParams?.expires_in //过期时间
  }

  //  判断是否获取到access_token
  if (queryParams.access_token) {
    permissionStore.setClientId(queryParams.client_id)
    authUtil.setToken(resToken)
    loading.value = false
    await router.push({ path: defaultRouter.value })
  } else {
    ;() => {
      ElNotification({
        title: '非常抱歉',
        message: '无法正常打开页面。请重新登录或联系管理员解决'
      })
    }
    window.location.hash = ''
  }
}

const developmentInit = async () => {
  const env = import.meta.env
  const resToken: resRokenRaw = {
    id: 0,
    accessToken: env.VITE_TOKEN, // 访问令牌
    userId: 1, // 用户编号
    userType: 1, //用户类型
    clientId: env.VITE_CLIENT_ID, //客户端编号
    expiresTime: env.VITE_EXPIRSE_IN //过期时间
  }
  permissionStore.setClientId(env.VITE_CLIENT_ID)
  authUtil.setToken(resToken)
  loading.value = false
  if (defaultRouter.value === '') return location.reload()
  await router.push({ path: defaultRouter.value })
}

watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    if (route.name === 'Oauth2Redirect') {
      import.meta.env.DEV ? developmentInit() : init(route)
    }
  },
  { immediate: true }
)

watch(
  () => defaultRouter.value,
  async (newVal) => {
    if (newVal == undefined) {
      const url = window.location.href
      const newUrl = url.replace('/oauth2', '/403')
      window.location.href = newUrl
    }
  },
  { deep: true, immediate: true }
)
</script>
