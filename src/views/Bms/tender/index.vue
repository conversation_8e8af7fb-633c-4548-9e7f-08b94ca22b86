<template>
  <ContentWrap>
    <span v-show="false"> {{ searchRef?.offsetHeight }}</span>
    <div ref="searchRef">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="search">
          <Search
            :schema="searchSchema(t, b, u)"
            @reset="setSearchParams"
            @search="setSearchParams"
          />
        </el-collapse-item>
      </el-collapse>
    </div>
  </ContentWrap>

  <el-card shadow="never">
    <div ref="tableTopRef">
      <Table
        :loading
        :border="true"
        :max-height="formatterTableHeight"
        v-model:currentPage="tableObject.currentPage"
        v-model:pageSize="tableObject.pageSize"
        :columns="tableColumns"
        :data="tableObject.tableList"
        :pagination="{
          total: tableObject.total
        }"
      >
        <template #name="{ row }">
          <span @click="handleEvent('edit', row)" class="active">{{ row.name }}</span>
        </template>
        <template #bidOpeningDate="{ row }">
          <span
            v-if="row.bidOpeningDate"
            :class="{ 'expired-date ': isWithinTwoWeeksBeforeBid(row.bidOpeningDate) }"
          >
            {{ dayjs(row.bidOpeningDate).format('YYYY-MM-DD') }}
          </span>
          <span v-else>--</span>
        </template>
        <template #bidPurchaseDate="{ row }">
          <span
            v-if="row.bidPurchaseDate"
            :class="{ 'expired-date': isDateExpired(row.bidPurchaseDate) }"
          >
            {{ dayjs(row.bidPurchaseDate).format('YYYY-MM-DD') }}
          </span>
          <span v-else>--</span>
        </template>

        <template
          v-for="col in [...projectMergeColumns, ...bagMergeColums]"
          #[col.slot]="{ row, $index }"
          :key="col.slot"
        >
          <MergedCell
            :data="row[col.field]"
            :field="col.field"
            :index="$index"
            :row="row"
            :height="row"
          />
        </template>
      </Table>
    </div>
  </el-card>

  <detailView ref="detailRef" @data-updated="async () => await getTableList()" />
</template>

<script setup lang="ts">
import * as Api from '@/api/Bms/tender/index'
import { tableColumns, projectMergeColumns, bagMergeColums, searchSchema } from './tender.data'
import dayjs from 'dayjs'
import MergedCell from './MergedCell/MergedCell.vue'
import detailView from './detail/detail.vue'
import { useElementHeight } from '@/hooks/web/useDomHeight'
import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import { cleanEmptyKeys } from '@/utils/formatter'
import { useOptionStoreWithOut } from '@/store/modules/bms/useOptionStore'
import { useBidDateUtils } from '@/views/Bms/hooks/useBidDateUtils'

const optionStore = useOptionStoreWithOut()

const { isWithinTwoWeeksBeforeBid, isDateExpired } = useBidDateUtils()
const { domRef: tableTopRef, domHeight: tableTopDomHeight } = useElementHeight()

const detailRef = ref<InstanceType<typeof detailView>>()
const searchRef = ref()

const loading = ref(false)
const activeNames = ref('search')

const { tableObject, tableMethods } = useTable({
  getListApi: async () => {
    const params: any = cleanEmptyKeys(tableObject.params, { removeEmptyString: true })

    if (params?.bidOpeningDateRange?.length === 2) {
      const [start, end] = params.bidOpeningDateRange
      params.bidOpeningDateStart = start
      params.bidOpeningDateEnd = end
      delete params.bidOpeningDateRange
    }

    const data = await Api.getBidTableList({
      ...params,
      pageSize: tableObject.pageSize,
      pageNo: tableObject.currentPage
    })
    const newData = formatterData(data?.list)
    return {
      list: newData,
      total: data?.total
    }
  }
})

const formatterTableHeight = computed(
  () =>{
    const searchHeight = searchRef.value?.clientHeight || 0
    const topHeight = tableTopRef.value?.clientHeight || 0
    return `calc(100vh - ${searchHeight + topHeight + 130}px)` // 减少固定偏移量
  }
  // `calc(100vh - ${(searchRef.value?.offsetHeight ?? 0) + tableTopDomHeight.value + 130}px)`
)

const { getList, setSearchParams } = tableMethods

const handleEvent = async (type: string, row?: { id: number }) => {
  switch (type) {
    case 'edit':
      detailRef.value?.open(row?.id)
      break
    default:
      console.error('操作有误')
      break
  }
}

/* 格式化数据 */
const formatterData = (data: any[]) => {
  return data?.map((item) => {
    return {
      ...item,
      // 是否中标
      // isBid: item.isBid.length
      //   ? item?.isBid?.map((obj: Record<string, string[]>) => {
      //     return getDictLabel(DICT_TYPE.BMS_IS_BID, obj)
      //   })
      //   : [0],
      subTypeMerge: item.subTypeMerge.length
        ? item?.subTypeMerge?.map((obj: Record<string, string[]>) => {
            return getDictLabel(DICT_TYPE.BMS_SUB_TYPE, obj)
          })
        : [0],
      // 报价方式
      quotationMethodMerge: item?.quotationMethodMerge?.length
        ? item?.quotationMethodMerge?.map((obj: Record<string, string[]>) => {
            return getDictLabel(DICT_TYPE.BMS_QUOTATION_METHOD, obj)
          })
        : [0],
      // 项目名称
      projectNameMerge: item?.projectNameMerge?.map((obj: Record<string, string[]>) => {
        const value = Object?.values(obj)[0]
        return value?.map((val) => {
          return val
        })
      }),
      // 预算金额
      budgetAmountMerge: item?.budgetAmountMerge?.map((obj: Record<string, string[]>) => {
        const value = Object?.values(obj)[0]
        return value?.map((val) => {
          const num = Number(val)
          return isNaN(num) ? val : num?.toFixed(2)
        })
      }),
      // 最高限额
      maximumLimitMerge: item?.maximumLimitMerge?.map((obj: Record<string, string[]>) => {
        const value = Object?.values(obj)[0]
        return value?.map((val) => {
          const num = Number(val)
          return isNaN(num) ? val : num?.toFixed(2)
        })
      }),
      pid2Quotation:
        item?.pid2Quotation?.map((obj: Record<string, any>) => {
          const heightMap = [30, 40, 50, 60]
          const firstKey = Object.keys(obj)[0]
          const quotations = obj[firstKey] || []

          // 合并所有 rows（双层数组展开）
          const allRows = quotations.flatMap((q: any) => q?.rows || [])

          // 有效行统计：至少有一个单元格对象含 id
          const validCount = allRows.reduce((count, row) => {
            const hasValid = Object.values(row).some(
              (cell) => typeof cell === 'object' && cell !== null && cell.id != null
            )
            return count + (hasValid ? 1 : 0)
          }, 0)

          const height = heightMap[Math.min(validCount, heightMap.length - 1)]

          // 给每个报价项添加 height 字段
          return quotations.map((q: any) => ({
            ...q,
            height
          }))
        }) || []
    }
  })
}

const getTableList = async () => {
  loading.value = true
  try {
    await getList()
    loading.value = false
  } catch (e) {
    loading.value = false
  }
}

interface OptionItem {
  label: string
  value: string
}
const t = ref<OptionItem[]>([]) // 技术负责人
const u = ref<OptionItem[]>([]) // 上传负责人
const b = ref<OptionItem[]>([]) // 商务负责人
const getOptions = async () => {
  b.value = optionStore.bList
  t.value = optionStore.tList
  u.value = optionStore.uList
}

const updateHeight = () => {
  if (searchRef.value) {
    searchRef.value = searchRef.value.offsetHeight
  }
}

let resizeObserver
onMounted(async () => {
  await getTableList()
  await getOptions()
  resizeObserver = new ResizeObserver(updateHeight)
  if (searchRef.value) {
    resizeObserver.observe(searchRef.value)
  }
  if (tableTopRef.value) {
    resizeObserver.observe(tableTopRef.value)
  }


})

onUnmounted(() => {
  if (resizeObserver && searchRef.value) {
    resizeObserver.unobserve(searchRef.value)
  }
})
</script>

<style lang="scss" scoped>
@import url(@/styles/collapse.scss);

.active {
  color: rgb(67, 146, 249);
  font-weight: bold;
  &:hover {
    color: rgb(67, 146, 249);
    font-weight: bold;
    cursor: pointer;
  }
}
.expired-date {
  color: red; /* Element UI 的错误色 */
  font-weight: bold;
}
:deep(.cell-full) {
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  height: 100%;
  padding: 0 !important;
}
:deep(.el-divider--horizontal) {
  margin: 1px 0;
}
</style>
