<template>
  <el-tooltip :content="text" placement="top" :disabled="!isOverflowing">
    <span ref="spanRef" class="ellipsis-item">{{ text || '--' }}</span>
  </el-tooltip>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

defineProps({
  text: {
    type: String,
    default: '--'
  }
})

const spanRef = ref(null)
const isOverflowing = ref(false)

const checkOverflow = () => {
  if (spanRef.value) {
    isOverflowing.value = spanRef.value.scrollWidth > spanRef.value.offsetWidth
  }
}

onMounted(() => {
  checkOverflow()
  if (spanRef.value) {
    const observer = new ResizeObserver(checkOverflow)
    observer.observe(spanRef.value)
    spanRef.value.__resizeObserver__ = observer
  }
})

onUnmounted(() => {
  if (spanRef.value && spanRef.value.__resizeObserver__) {
    spanRef.value.__resizeObserver__.disconnect()
  }
})
</script>

<style>
.ellipsis-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: inline-block;
}
</style>
