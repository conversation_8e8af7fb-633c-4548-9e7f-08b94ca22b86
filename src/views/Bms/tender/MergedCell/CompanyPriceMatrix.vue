<template>
  <el-tooltip placement="top" v-if="TEXT">
    <template #content>
      <table class="custom-table">
        <thead>
          <tr>
            <th
              v-for="col in tableColums"
              :key="col.field"
              :class="{ 'green-text': col.options === 'identification' }"
            >
              {{ col.label }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in tableData" :key="rowIndex">
            <td
              v-for="col in tableColums"
              :key="col.field"
              :class="{ 'green-text': col.options === 'identification' }"
            >
              {{
                (() => {
                  const cell = row[col.field]
                  if (!cell) return '-'

                  const method = quotationMethod
                  const val = method === 3 ? cell.quotationRate : cell.quotationFina

                  if (!val) return '-'
                  return val
                })()
              }}
            </td>
          </tr>
        </tbody>
      </table>
    </template>

    <div class="price-table" ref="containerRef">
      <div
        v-for="(item, index) in TEXT"
        :key="index"
        :class="[
          'text-item',
          { 'green-text': item.isGreen },
          { 'no-margin': index === TEXT.length - 1 }
        ]"
      >
        <span v-html="item.text"></span>
        <span v-if="index < TEXT.length - 1">｜</span>
      </div>
    </div>
  </el-tooltip>
  <span v-else>--</span>

  <!-- <div class="price-table" ref="containerRef" v-else>
    <div
      v-for="(item, index) in TEXT"
      :key="index"
      :class="[
        'text-item',
        { 'green-text': item.isGreen },
        { 'no-margin': index === TEXT.length - 1 }
      ]"
    >
      <span v-html="item.text"></span>
      <span v-if="index < TEXT.length - 1">｜</span>
    </div>
  </div> -->
</template>

<script setup lang="ts">
defineOptions({ name: 'CompanyPriceTable' })
import { computed } from 'vue'
import * as _ from 'lodash-es'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'
const props = defineProps<{
  data: any
}>()

const useTenderStore = useTenderStoreWithOut()

const quotationMethod = computed(() => useTenderStore.getQuotationMethod) // 报价方式

const tableColums = computed(() =>
  _.cloneDeep(props.data.headers).filter((item) => item.field !== 'rounds')
)

const tableData = computed(() => _.cloneDeep(props.data?.rows || []))
const TEXT = computed(() => {
  const cloneData: any = _.cloneDeep(props?.data)

  const { headers, rows } = cloneData
  const fields = headers?.filter((item) => item.field !== 'rounds')
  if (!fields) return false
  return fields.map(({ field, label, options }) => {
    const values = rows.map((row) => {
      const cell = row[field]
      if (!cell) return '-' // 空对象保护
      const val = quotationMethod.value === 3 ? cell.quotationRate : cell.quotationFina
      if (val == null || val === '') return // 空值显示 -
      // return quotationMethod.value === 1 ? `${val}%` : val
      return val
    })

    function joinUntilEmpty(values: any[]) {
      const result: Array<string> = []
      for (const val of values) {
        if (val === null || val === undefined || val === '') break
        result.push(val)
      }
      // return label + '：' + result.join('/')
      return label
    }

    return {
      text: joinUntilEmpty(values),
      isGreen: options === 'identification'
    }
  })
})

// --------------------- 监听容器尺寸 ---------------------
const containerRef = ref<HTMLElement | null>(null)
const isOverflow = ref(false)

const checkOverflow = () => {
  if (!containerRef.value) return
  const dom = containerRef.value
  isOverflow.value = dom.scrollWidth > dom.offsetWidth
}

let resizeObserver: ResizeObserver | null = null

onMounted(() => {
  nextTick(() => {
    if (containerRef.value) {
      resizeObserver = new ResizeObserver(() => {
        checkOverflow()
      })
      resizeObserver.observe(containerRef.value)
    }
    checkOverflow()
  })
})

onBeforeUnmount(() => {
  if (resizeObserver && containerRef.value) {
    resizeObserver.unobserve(containerRef.value)
    resizeObserver.disconnect()
    resizeObserver = null
  }
})

watch(
  () => TEXT.value,
  async () => {
    await nextTick()
    checkOverflow()
  }
)
</script>

<style scoped lang="scss">
.custom-table {
  line-height: 18px;
  border-collapse: separate; /* 必须设置为 separate 才能使用 border-spacing */
  border-spacing: 10px 0; /* 水平方向间距 8px，垂直方向间距 0，可按需调整 */
  text-align: center;
  font-size: 14px;
}
.green-text {
  color: #67c23a;
  font-weight: bold;
}

.no-margin {
  padding-left: 4px;
}
.text-item {
  display: inline-block;
}
.price-table {
  font-size: 12px;
  width: auto;
  line-height: 16px;
  text-align: center;
  // display: flex;
  // flex-direction: row;
  // align-items: center;
  // justify-content: center;
}
</style>
