<template>
  <div class="cell-full-view">
    <template v-if="isShowBagMerge">
      <template v-for="(name, i) in flatValue" :key="i">
        <!--        :class="['cell-item-more', { 'cell-item-more-bagName': WRAP_TEXT_FIELDS.includes(field) }]"-->
        <div
          :style="{ height: alignHeights[i] ? alignHeights[i] + 'px' : LINE_HEIGHT[i] + 'px' }"
          class="cell-item-more cell-item-more-bagName"
        >
          <EllipsisTooltip :text="name?.[0]" />
        </div>
        <!-- 添加实线分割线 -->
        <el-divider v-if="i < flatValue.length - 1" class="group-divider" />
      </template>
    </template>

    <template v-if="isShowProjectMerge">
      <template v-for="(group, i) in flatValue" :key="i">
        <div class="cell-item-more" :style="{ height: groupHeight(group) + 'px' }">
          <template v-if="Array.isArray(group)">
            <template v-for="(subGroup, j) in group" :key="j">
              <div v-if="Array.isArray(subGroup)">
                <div v-for="(item, k) in subGroup" :key="k" class="line-item">
                  <span>{{ item || '--' }} </span>
                </div>
              </div>

              <!-- 每个项目的报价 -> 字段并且里面有值  -->
              <div v-else-if="isShowSubDomain && subGroup">
                <CompanyPriceMatrix :data="subGroup" />
              </div>
              <EllipsisTooltip v-else :text="subGroup" />
              <!--  内部分隔虚线 -->
              <el-divider v-if="j < group.length - 1" class="sub-divider" />
            </template>
          </template>

          <template v-else>
            <EllipsisTooltip :text="group" />
          </template>
        </div>
        <!--  外层组之间实线分隔 -->
        <el-divider v-if="i < flatValue.length - 1" class="group-divider" />
      </template>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { computed, onBeforeUnmount, onMounted, watch } from 'vue'
import CompanyPriceMatrix from './CompanyPriceMatrix.vue'
import { BAG_MERGE_FIELDS, PROJECT_MERGE_FIELDS, SUB_DOMAIN_FIELDS } from '../tender.data'
import EllipsisTooltip from '@/views/Bms/components/ellipsisTooltip/EllipsisTooltip.vue'
const props = defineProps<{
  data: any
  index: number
  field: string
  row?: Record<string, any>
  height: any
}>()
// 在JS中定义需要强制换行的字段（可配置）
const WRAP_TEXT_FIELDS = [
  'bagNameMerge',
  'subBidNameMerge',
  'subBidNumberMerge',
  'businessDirectorMerge',
  'technicalDirectorMerge'
] // 扩展其他字段

// 平分的行高
const LINE_HEIGHT = computed(() => {
  if (props?.field == SUB_DOMAIN_FIELDS) {
    return props.data?.height ?? 30
  }
  return 30
})

const isShowBagMerge = computed(() => BAG_MERGE_FIELDS.includes(props.field))
const isShowProjectMerge = computed(() => PROJECT_MERGE_FIELDS.includes(props.field))
const isShowSubDomain = computed(() => props.field == SUB_DOMAIN_FIELDS)

// 规范化数据结构为二维数组结构
const flatValue = computed(() => {
  const d = props.data
  if (!Array.isArray(d)) return [d]
  if (!Array.isArray(d[0])) return d.map((i) => [i])
  return d
})

// 项目合并行的高度
const groupHeight = (group: any): number => {
  if (!Array.isArray(group)) return LINE_HEIGHT.value
  return (
    group.reduce((acc, item) => acc + (Array.isArray(item) ? item.length : 1), 0) *
    LINE_HEIGHT.value
  )
}

// 包名称根据项目高度对齐
const alignHeights = computed(() => {
  const reference = props.row?.[PROJECT_MERGE_FIELDS[0]]
  if (!Array.isArray(reference)) return []
  return reference.map(groupHeight)
})

watch(
  () => flatValue.value,
  async () => {
    // checkOverflow()
  },
  { deep: true, immediate: true }
)

onMounted(async () => {})

onBeforeUnmount(() => {})
</script>

<style lang="scss" scoped>
.cell-full-view {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;
  white-space: nowrap; // 不换行
}

.cell-item-more {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
}

.cell-item-more-bagName {
  @extend .cell-item-more;
}

.line-item {
  text-align: center;
}

.ellipsis-item {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 分割线：左右超出 12px */
.group-divider,
.sub-divider {
  width: calc(100% + 24px);
  transform: translateX(-12px);
}

/* 特殊列：强制换行 */
.cell-item-more-bagName .wrap-text {
  white-space: normal !important;
  word-wrap: break-word !important;
  display: inline-block;
  width: 100%;
}
</style>
