<template>
  <el-table :data="tableData" border style="width: 35vw" size="small">
    <!-- 动态表头 -->
    <el-table-column
      v-for="col in columns"
      :show-overflow-tooltip="true"
      :key="col.field"
      :prop="col.field"
      :label="col.label"
      :width="col.width"
      :min-width="col.minWidth"
      :align="col.align"
    >
      <template #default="{ row }">
        <!-- 可编辑字段 -->
        <template v-if="isEditing && editableFields.includes(col.field)">
          <!-- ratio 用数字输入 -->
          <el-input-number
            v-if="col.field === 'ratio'"
            v-model="row.ratioDisplay"
            :controls="false"
            :min="0"
            :max="100"
            size="small"
            @change="handleRatioChange(row)"
            style="width: 100%"
          />
          <span v-else-if="col.field === 'action'">
            <el-button
              type="danger"
              :icon="Delete"
              circle
              size="small"
              @click="handleDeleteItem(row)"
            />
          </span>
          <el-input v-else v-model="row[col.field]" size="small" type="text" />
        </template>

        <!-- 只读展示 -->
        <template v-else>
          <span v-if="col.field === 'total'">{{ row.total?.toFixed(2) || '-' }}</span>
          <span v-else-if="col.field === 'ratio'">{{ Math.round(row.ratio * 100) }}%</span>
          <span v-else>{{ row[col.field] || '--' }}</span>
        </template>
      </template>
    </el-table-column>
  </el-table>

  <div class="bottom-view">
    <!--  flex 布局左侧占位 -->
    <div v-if="!isEditing" style="flex: 1"></div>

    <el-button v-if="isEditing" size="small" class="mt-2" style="width: 40%" @click="handleAddItem">
      新增
    </el-button>

    <!-- 总报价输入 -->
    <div style="margin-top: 10px; text-align: right">
      <span class="totle-view">合计（万元，含税6%）：</span>
      <div class="totalPrice">{{ totalPrice ?? 0 }} 万元</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import { ElMessage } from 'element-plus'
import useFullScreenLoader from '@/hooks/web/useFullScreenLoader'
import { Delete } from '@element-plus/icons-vue'
import * as Api from '@/api/Bms/tender/index'
import * as _ from 'lodash-es'

const { withLoader } = useFullScreenLoader()
interface SubmitParams {
  domain: string
  subId: string | number
}

const props = defineProps<{
  data: any
  isSave: boolean
  submitParams: SubmitParams
}>()

const PropsTableData = computed(() => props?.data)

/* ---------- 表头 ---------- */
const columns: any = ref([])

/* ---------- 数据 ---------- */
const tableData: any = ref([])

/* 新增项目 */
const handleAddItem = () => {
  const newItem = {}

  // 如果 header 中有动态字段（如 disabled 的自动计算项），初始化为 null
  columns.value.forEach((col) => {
    if (!(col.field in newItem)) {
      newItem[col.field] = null
    }
  })
  tableData.value.push(newItem)

  // 如果已设置总价，自动计算新条目的价格与合计
  if (totalPrice.value) recalculatePrices()
}

/* 移除项目 */
const handleDeleteItem = (row) => {
  const index = tableData.value.indexOf(row)
  if (index !== -1) {
    tableData.value.splice(index, 1)
  }
}

/* 初始化 ratioDisplay 字段（整数百分比） */
tableData.value.forEach((row) => {
  return (row.ratioDisplay = Math.round(row.ratio * 100))
})

/* ---------- 其他状态 ---------- */
const editableFields = computed(() =>
  columns.value.filter((col) => !col?.disabled).map((col) => col.field)
)

const totalPrice = ref<any>(null)

const isEditing = computed(() => props.isSave) // 控制是否可以修改

/* ---------- 公共函数：根据总价 & 比重重新计算 ---------- */
function recalculatePrices() {
  if (!totalPrice.value) return

  const calculatedFields = columns.value
    .filter((col) => col.options === 'calculate' && col.disabled)
    .map((col) => col.field)

  tableData.value.forEach((row) => {
    const calculatedValue = +(totalPrice.value * row.ratio).toFixed(2)
    row.price = calculatedValue
    row.total = calculatedValue

    // 将 total 值赋给所有自动计算字段
    calculatedFields.forEach((field) => {
      row[field] = calculatedValue
    })
  })
}

/* ---------- 编辑模式切换 ---------- */
async function toggleEdit() {
  if (!isEditing.value) {
    /* 进入编辑：同步 ratioDisplay */
    tableData.value.forEach((row) => (row.ratioDisplay = Math.round(row.ratio * 100)))
  } else {
    // 校验每一行 ratio 有值且不能为 0
    const invalidRow = tableData.value.find((row) => {
      return row.ratio === null || row.ratio === undefined || row.ratio <= 0
    })
    if (invalidRow) {
      ElMessage.error('检测到：有价格比重未填写！')
      return false
    }

    // 完成编辑时统一校验 ratio 总和
    const sum = tableData.value.reduce((acc, cur) => acc + cur.ratio, 0)
    const fixedSum = +sum.toFixed(6) // 防止浮点精度误差
    if (fixedSum !== 1) {
      ElMessage.error('价格比重总和必须等于 100%')
      return false // 阻止退出编辑模式
    }

    /* 完成编辑：可在此提交 */
    const cloneData = _.cloneDeep(tableData.value)
    const fieldsToRemove = ['price', 'ratio', 'ratioDisplay', 'total', 'number']

    const filteredData = cloneData.map((item) => {
      const result: Record<string, any> = {}
      // 添加 weight 字段
      result.weight = item.ratio
      // 复制除去 fieldsToRemove 之外的字段
      for (const key in item) {
        if (!fieldsToRemove.includes(key)) {
          result[key] = item[key]
        }
      }
      return result
    })

    const params = {
      subDomain: props.submitParams?.domain,
      subId: props.submitParams?.subId,
      rows: JSON.stringify(filteredData)
    }
    await withLoader(Api.detailedUpdate(params))
    ElMessage({ message: '操作成功', type: 'success' })
    return true
  }
}

/* ---------- 处理比重变更 ---------- */
function handleRatioChange(row) {
  /* 输入校验 0 ~ 100 */
  row.ratioDisplay = Math.min(Math.max(row.ratioDisplay, 0), 100)
  row.ratio = row.ratioDisplay / 100
  /* 若已填写总报价，则立即重新计算单价/合计 */
  if (totalPrice.value) recalculatePrices()
}

/* ---------- 监听 amount / price → 自动算 total ---------- */
watch(
  () => tableData.value,
  () => {
    tableData.value?.forEach((row) => {
      if (row.amount && row.price) {
        row.total = +(row.amount * row.price).toFixed(2)
      }
    })
  }
)

/* ---------- 根据操作状态控住删除列展示 ---------- */
// 监听编辑状态，动态控制 "操作" 和 "价格比重" 列是否显示
watch(
  () => isEditing.value,
  (newVal) => {
    const hasActionColumn = columns.value.some((col) => col.field === 'action')
    const hasRatioColumn = columns.value.some((col) => col.field === 'ratio')

    if (newVal) {
      // 添加“价格比重”列
      if (!hasRatioColumn) {
        columns.value.push({
          field: 'ratio',
          label: '价格比重',
          width: 100,
          align: 'center'
        })
      }
      // 添加“操作”列
      if (!hasActionColumn) {
        columns.value.push({
          field: 'action',
          label: '操作',
          width: 60,
          align: 'center'
        })
      }
    } else {
      // 移除“操作”列
      if (hasActionColumn) {
        columns.value = columns.value.filter((col) => col.field !== 'action')
      }

      // 移除“价格比重”列
      if (hasRatioColumn) {
        columns.value = columns.value.filter((col) => col.field !== 'ratio')
      }
    }
  }
)

/* ---------- 有新的明细数据之后 处理数据 -> 赋值 ---------- */
watch(
  () => PropsTableData.value,
  (newVal) => {
    if (!newVal) return
    totalPrice.value = Number(newVal.amount)
    columns.value = [
      ...(Array.isArray(newVal?.header)
        ? newVal?.header.map((item) => ({
            ...item,
            align: 'center'
          }))
        : [])
    ]

    tableData.value = newVal?.rows?.map((row) => {
      const { weight, ...rest } = row
      return {
        ...rest,
        ratio: weight,
        ratioDisplay: weight * 100
      }
    })

    recalculatePrices()
  }
)

defineExpose({ toggleEdit })
</script>

<style scoped lang="scss">
:deep(.el-input__inner) {
  text-align: center !important;
}
.bottom-view {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.totle-view {
  color: #303133;
  font-size: 15px;
  font-weight: 500;
}
.totalPrice {
  color: #303133;
  display: inline-block;
  min-width: 90px;
  text-align: left;
  font-weight: bold;
}
</style>
