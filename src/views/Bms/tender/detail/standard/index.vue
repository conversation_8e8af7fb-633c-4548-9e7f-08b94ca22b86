<template>
  <divider
    v-model:isSave="isSave"
    :isDisabled="!serviceType"
    @handel-save="handelSave"
    class="mb-3"
    title="报价明细"
    :selectData="[]"
    @close="handelClose"
  >
    <template #default>
      <el-cascader
        @update:modelValue="handelCascaderChange"
        style="width: 120px"
        size="small"
        v-model="cascaderData"
        placeholder="请选择"
        :options="cascaderOptions"
        filterable
      />
    </template>
  </divider>

  <TableView
    :data="tableData"
    :submitParams="tableSubmitParams"
    :isSave="isSave"
    ref="tableRef"
    v-if="serviceType"
  />
  <el-empty description="当前无服务类型，无法操作" v-if="!serviceType" />
</template>

<script setup lang="ts">
defineOptions({ name: 'StandardView' })
import { ref } from 'vue'
import divider from '@/views/Bms/components/divider/index.vue'
import TableView from './table.vue'
const emit = defineEmits(['success'])
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'
import * as Api from '@/api/Bms/tender/index'

const props = defineProps({
  isReset: {
    type: Number
  }
})

const useTenderStore = useTenderStoreWithOut()

const storeQuoteInfo = computed(() => useTenderStore.getQuoteInfo) // 项目的信息
const serviceType = computed(() => useTenderStore.getServiceType) // 服务类型
const bagInfo = computed(() => useTenderStore.getBagInfo) // 服务类型
const tableRef = ref<InstanceType<typeof TableView>>()
const isSave = ref(false)
const tableData = ref()
const tableSubmitParams = ref()

const cascaderOptions = ref() // 联级下拉数据
const cascaderData: Ref<[number, number] | []> = ref([]) // 联级下拉选中的数据

const getRoundData = async () => {
  const roundOptions: any[] = await Api.getRoundOptions(bagInfo.value?.id)
  cascaderOptions.value = roundOptions
  cascaderData.value = [
    Number(roundOptions[0]?.value),
    Number(roundOptions[0]?.children?.[0]?.value)
  ]
}

const getData = async () => {
  if (!serviceType.value) return
  const params = {
    subId: storeQuoteInfo.value?.projectInfo?.subId,
    domain: Number(cascaderData.value?.[0]),
    rounds: Number(cascaderData.value?.[1])
  }
  tableSubmitParams.value = { ...params }
  const data = await Api.getTempDetail(params)
  tableData.value = data
}

const handelCascaderChange = async (val) => {
  cascaderData.value = val
  await getData()
}

const handelSave = async () => {
  const isSuccess = await tableRef.value?.toggleEdit()
  if (isSuccess) await getData()
  if (isSuccess) {
    emit('success')
    isSave.value = false
  }
}

const handelClose = async () => {
  await getData()
  isSave.value = false
}

watch(
  () => props.isReset,
  () => {
    getData()
  }
)

// 用于记录变化次数
const changeCount = ref(0)

watch(
  () => [serviceType.value, bagInfo.value],
  // 当任意一个发生变化时触发
  async () => {
    // 每次 watch 被触发时，将计数器加 1
    changeCount.value++
    // 当变化触发累计到第 3 次时，执行请求
    if (changeCount.value >= 2) {
      await getRoundData()
      await getData()
    }
  },
  {
    immediate: true
  }
)
</script>

<style lang="scss" scoped></style>
