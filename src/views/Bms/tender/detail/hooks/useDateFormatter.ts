export function useDateFormatter() {
  const safeFormatDateToISO = (date: Date | string | null | undefined): string | undefined => {
    if (date == null) return undefined;
    if (date instanceof Date) return date.toISOString();
    if (typeof date === "string") {
      // ISO 格式直接返回
      if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/.test(date)) return date;

      const parsedDate = new Date(date);
      if (!isNaN(parsedDate.getTime())) return parsedDate.toISOString();
    }
    throw new Error(`无效的日期格式: ${date}`);
  };

  return { safeFormatDateToISO };
}
