import { ref } from 'vue'
import { cloneDeep } from 'lodash-es'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'

export const useQuotationTable = (rawData: any) => {
  const { headers, rows: TableRows, currentQuotation, maximumLimit, quotationMethod } = rawData
  const cloneHeaders = cloneDeep(headers)
  const useTenderStore = useTenderStoreWithOut()
  const result = cloneHeaders
    .filter((item) => item.field !== 'rounds')
    .map(({ field, label, options }) => ({
      value: field,
      label: options === 'identification' ? `${label}（主）` : label,
      options
    }))

  useTenderStore.setCompanyList(result)
  // 生成表头
  const tableColumns = headers?.map((header: any) => ({
    label: header?.options === 'identification' ? `${header?.label}（主）` : header?.label,
    prop: header.field === 'rounds' ? 'batch' : Number(header.field),
    options: header.options
  }))

  const tableData = ref({
    calcData: [] as any[],
    dataRow: [] as any[]
  })

  const calcData: any[] = []
  const dataRows: any[] = []
  TableRows?.forEach((row: any) => {
    const calcRow: Record<string, any> = {
      batch: `第${row.rounds}轮`
    }
    const dataRow: Record<string, any> = {
      batch: `第${row.rounds}轮`
    }

    for (const key in row) {
      if (['rounds', 'disabled'].includes(key)) continue
      const cell = row[key]
      if (cell && typeof cell === 'object') {
        calcRow[key] = cell.quotationFina
        dataRow[key] = cell.quotationRate
        dataRow.id = cell.id
      }
    }

    calcData.push(calcRow)
    dataRows.push(dataRow)
  })

  tableData.value.calcData = calcData
  tableData.value.dataRow = dataRows

  return {
    tableColumns,
    tableData,
    currentRound: currentQuotation,
    maxPrice: maximumLimit,
    quotationMethod
  }
}



// 轮次枚举
export  enum BatchRoundMap {
  '第1轮' = 1,
  '第2轮' = 2,
  '第3轮' = 3
}

// 获取数字轮次
export const getRoundNumber = (batch: string): number => {
  return BatchRoundMap[batch as keyof typeof BatchRoundMap] || 0
}

// 获取文字轮次
export const getRoundLabel = (round: number): string => {
  return BatchRoundMap[round] || ''
}


export function transformToApiFormat(data) {
  return {
    quotationBatches: data.map((row) => {
      const rounds = row.rounds
      const quotations = Object.entries(row)
        .filter(([key]) => !isNaN(Number(key))) // 只处理 "1"、"3" 这样的键
        .map(([key, val]) => ({
          ...val,
          quotationDomain: Number(key)
        }))
      return { rounds, quotations }
    })
  }
}

export function mergeQuotationRate(raw, target) {
  raw.forEach((item) => {
    const rounds = BatchRoundMap[item.batch]
    if (!rounds) return
    const targetRow = target.find((row) => row.rounds === rounds)
    if (!targetRow) return
    Object.keys(item).forEach((key) => {
      if (key === 'batch') return
      if (targetRow[key] && typeof targetRow[key] === 'object') {
        targetRow[key].quotationRate = item[key]
      }
    })
  })

  return target
}



export const defaultData = {
    "id": 13,
    "maximumLimit": 100,
    "currentQuotation": 1,
    "headers": [
        {
            "field": "rounds",
            "label": "轮次",
            "disabled": null,
            "options": null
        },
        {
            "field": "1",
            "label": "惠卓",
            "disabled": null,
            "options": "identification"
        },
        {
            "field": "2",
            "label": "梦策",
            "disabled": null,
            "options": null
        }
    ],
    "rows": [
        {
            "1": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 1
            },
            "2": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 1
            },
            "disabled": null,
            "rounds": 1
        },
        {
            "1": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 2
            },
            "2": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 2
            },
            "disabled": null,
            "rounds": 2
        },
        {
            "1": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 3
            },
            "2": {
                "quotationRate": null,
                "quotationFina": null,
                "id": null,
                "state": null,
                "quotationDomain": null,
                "rounds": 3
            },
            "disabled": null,
            "rounds": 3
        }
    ]
}