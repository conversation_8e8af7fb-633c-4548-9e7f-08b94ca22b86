<template>
  <div class="price-discount-table" style="width: 35vw">
    <!-- 上方金额表 -->
    <el-table :data="tableData?.calcData" :border="true" size="small">
      <el-table-column prop="batch" :align="'center'">
        <template #header>
          <div class="slice-header">
            <svg class="diagonal-line" viewBox="0 0 100 100" preserveAspectRatio="none">
              <line x1="0" y1="0" x2="100" y2="100" stroke="#999" stroke-width="2" />
            </svg>
            <span class="corner bottom-left">轮次</span>
            <span class="corner top-right">公司</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-for="col in tableColumns.slice(1)"
        :key="col.prop"
        :prop="col.prop.toString()"
        :label="col.label"
        :align="'center'"
      >
        <template #default="{ row, $index }">
          {{ shouldFormat($index, col.prop) ? formatPrice(row[col.prop]) : row[col.prop] }}
        </template>
      </el-table-column>
      <el-table-column label="操作" :align="'center'" v-if="!editable" width="60"
        >/</el-table-column
      >
    </el-table>

    <!-- 下方折扣率表 -->
    <el-table
      :data="tableData?.dataRow"
      :border="true"
      style="width: 100%"
      :show-header="false"
      size="small"
      class="mt-3"
    >
      <el-table-column prop="batch" label="轮次" :align="'center'" />
      <el-table-column
        v-for="col in tableColumns.slice(1)"
        :key="col.prop"
        :prop="col.prop.toString()"
        :label="col.label"
        :align="'center'"
      >
        <template #default="{ row, $index }">
          <!-- <div v-if="editable && currentRound == getRoundNumber(row.batch)"> -->
          <div v-if="editable && !isLockDisabled(row)">
            <el-input-number
              style="width: 80%"
              v-model="row[col?.prop]"
              v-on:update:modelValue="(val: number) => handleCellChange(val, $index, col.prop)"
              size="small"
              :controls="false"
              :min="1"
              :max="inputMode === 'discount' ? maxPrice : 100"
              class="discount-input"
            />
          </div>
          <div v-else> {{ row[col.prop] || '--' }}<span v-if="row[col.prop]">%</span> </div>
        </template>
      </el-table-column>

      <el-table-column :align="'center'" width="60" v-if="!editable">
        <template #default="{ row }">
          <el-button
            v-if="!isLockDisabled(row)"
            type="warning"
            link
            size="small"
            @click="handelRoundLock(row)"
            :disabled="isLockDisabled(row)"
          >
            锁定
          </el-button>
          <el-text size="small" type="danger" style="font-weight: bold" v-else>已锁定</el-text>
        </template>
      </el-table-column>
    </el-table>
  </div>

  <lineChartView ref="lineChartRef" />
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import * as Api from '@/api/Bms/tender'
import lineChartView from './lineChart.vue'
import useFullScreenLoader from '@/hooks/web/useFullScreenLoader'
import * as _ from 'lodash-es'
import {
  useQuotationTable,
  transformToApiFormat,
  mergeQuotationRate,
  getRoundNumber,
  defaultData
} from './useQuotationTable'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'

const emit = defineEmits(['update:editable', 'update:mask'])
const { withLoader } = useFullScreenLoader()

const props = defineProps<{
  isSave: boolean
}>()

/*
 * tableColumns 表格头
 * tableData 表格内容
 * currentRound 当前的轮数 可编辑
 * maxPrice 最高金额 （仅在 price 模式下生效）
 * inputMode 报价方式 外部控制 discount折扣率 price单价报价 total总价报价
 * editable  是否可编辑
 */
const tableColumns = ref<any[]>([])
const tableData = ref<{ calcData: any[]; dataRow: any[] }>({ calcData: [], dataRow: [] })
const currentRound = ref()
const maxPrice = ref(0)
const inputMode = ref<'price' | 'discount' | 'total'>()
const editable = computed(() => props.isSave)
const modifiedCells = ref<Set<string>>(new Set()) // 格式化追踪结构
const lineChartRef = ref<InstanceType<typeof lineChartView>>()

const useTenderStore = useTenderStoreWithOut()
const projectInfo = computed(() => useTenderStore.getQuoteInfo?.projectInfo?.id)
const subDomain = computed(() => useTenderStore.getQuoteInfo?.subDomain)
const storeQuotationMethod = computed(() => useTenderStore.getQuotationMethod)

/*
 * 报价方式映射 维护需跟字典变动
 * discount 折扣率报价 total 总价报价 price单体报价
 * 只有折扣率 按照百分比 */
const quotationMethodMap: Record<number, 'total' | 'discount' | 'price'> = {
  1: 'discount',
  2: 'total',
  3: 'price'
}

// 是否应格式化当前 cell
const shouldFormat = (rowIndex: number, key: string) => {
  return editable.value && modifiedCells.value.has(`${rowIndex}:${key}`)
}

const handleCellChange = (val: number, rowIndex: number, key: string) => {
  tableData.value.dataRow[rowIndex][key] = val // 更新输入表格的值
  tableData.value.calcData[rowIndex][key] = formatPrice(val) // 更新上方金额表对应位置的值
}

// 根据报价方式计算金额函数
const formatPrice = (percent: string | number | undefined) => {
  if (inputMode.value === 'discount') {
    const num = parseFloat(percent as string)
    return isNaN(num) ? '' : Number.isInteger(num) ? num.toString() : num.toFixed(2)
  }

  const val = parseFloat(percent as string)
  if (isNaN(val)) return ''
  const result = (val / 100) * maxPrice.value
  return Number.isInteger(result) ? result.toString() : result.toFixed(2)
}

const handleChart = () => {
  lineChartRef.value?.open(tableData.value, tableColumns.value, inputMode.value)
}

const retchData = ref()
const reRetchData = async () => {
  const data = await Api.getQuotationDetail(projectInfo.value)
  if (!data) {
    emit('update:mask', true)
  }
  retchData.value = _.cloneDeep(data ?? defaultData)
  // 使用 hook 处理返回的数据
  const parsed = useQuotationTable(data ?? defaultData)
  tableColumns.value = parsed?.tableColumns //表头
  tableData.value.calcData = parsed.tableData.value?.calcData // 计算的表格数据
  tableData.value.dataRow = parsed.tableData.value?.dataRow // 编辑的表格数据
  currentRound.value = parsed?.currentRound //当前轮数
  maxPrice.value = parsed?.maxPrice // 最高金额
  inputMode.value = quotationMethodMap[storeQuotationMethod.value] // 报价方式
  // 清空改动记录
  modifiedCells.value.clear()
}

// 是否禁用锁定按钮
const isLockDisabled = (data) => {
  const rounds = getRoundNumber(data.batch)
  const findData = retchData.value?.rows.find((item) => item.rounds === rounds)
  return Object.values(findData).some((value: { state }) => {
    return typeof value === 'object' && value !== null && value.state == true
  })
}

// 处理锁定行需要的数据格式
function getQuotationsByRound(
  data: Record<string, any>[],
  round: number
): { id: number | string; quotationRate: number; quotationDomain: number }[] {
  const roundLabel = round
  const target = data.find((item) => item.batch === roundLabel)
  if (!target) return []
  return Object.entries(target)
    .filter(([key, value]) => key !== 'batch' && key !== 'state' && key !== 'id' && value != null)
    .map(([key, value]) => ({
      id: target.id || null,
      quotationRate: value as number,
      quotationDomain: Number(key)
    }))
}

// 校验当前轮次数据的函数
function validateCurrentRoundData(data: { dataRow: any[] }, round: number): boolean {
  const currentBatchLabel = round
  const rows = data?.dataRow || []
  const currentIndex = rows.findIndex((item) => item.batch === currentBatchLabel)
  const currentBatchData = rows[currentIndex]
  if (!currentBatchData) {
    ElMessage({ message: '找不到当前轮次数据', type: 'warning' })
    return false
  }

  const excludeKeys = ['batch', 'id', 'quotationDomain', 'rounds', 'state']
  const hasNull = Object.entries(currentBatchData).some(
    ([key, value]) => !excludeKeys.includes(key) && value == null
  )

  if (hasNull) {
    ElMessage({ message: '当前轮次存在未填写的报价项', type: 'warning' })
    return false
  }

  return true
}

// 锁定当前行的轮次：1.校验当前行是否都填写 2.处理提交数据 3. api提交
const handelRoundLock = async (val) => {
  const cloneData: any = _.cloneDeep(tableData.value)
  if (!validateCurrentRoundData(cloneData, val?.batch)) return false
  const params = {
    id: projectInfo?.value,
    rounds: getRoundNumber(val?.batch),
    quotations: getQuotationsByRound(cloneData?.dataRow, val?.batch)
  }
  await withLoader(Api.quotationLock(params))
  ElMessage({ message: '锁定成功', type: 'success' })
  await reRetchData()
}

// 提交保存操作
const handleToggleEdit = async () => {
  if (editable.value) {
    try {
      const cloneData = _.cloneDeep(tableData.value)
      const mergeRateData = _.cloneDeep(
        mergeQuotationRate(cloneData?.dataRow, retchData.value.rows)
      )
      const params = {
        id: projectInfo?.value,
        ...transformToApiFormat(mergeRateData)
      }
      await withLoader(Api.quotationUpdate(params))
      ElMessage({ message: '保存成功', type: 'success' })
      await reRetchData()
      return true
    } catch (error) {
      console.error('提交失败:', error)
      ElMessage({ message: '提交失败，正在重新获取数据', type: 'error' })
    }
  }
}

defineExpose({ handleChart, handleToggleEdit, reRetchData })

watch(
  () => currentRound.value,
  (newVal) => {
    emit('update:editable', Number(newVal) > 3)
  }
)

watch(
  () => inputMode.value,
  (newVal) => {
    emit('update:mask', !newVal)
  },
  { immediate: true, deep: true }
)

// 用于记录变化次数
const changeCount = ref(0)

watch(
  [projectInfo, subDomain],
  async () => {
    changeCount.value++
    // 当变化触发累计到第 3 次时，执行请求
    if (changeCount.value >= 2) {
      await reRetchData()
    }
  },
  {
    immediate: true
  }
)
</script>

<style scoped lang="scss">
.price-discount-table {
  margin: 10px 0;
}
:deep(.el-input__inner) {
  text-align: center !important;
}
.discount-input {
  width: 95%;
}
.slice-header {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 26px;
  text-align: center;
  .diagonal-line {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  .corner {
    position: absolute;
    font-size: 12px;
    color: #333;
  }

  .bottom-left {
    bottom: -6px;
    left: 38px;
  }

  .top-right {
    top: -6px;
    right: 38px;
  }
}
</style>
