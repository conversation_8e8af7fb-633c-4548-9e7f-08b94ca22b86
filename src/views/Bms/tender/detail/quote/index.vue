<template>
  <div class="quote-view" v-show="!showMask">
    <divider
      title="项目报价"
      v-model:isSave="isSave"
      @handel-save="handelSave"
      :isDisabled="isEditableDisabled"
      :component-id="'quote'"
      @close="handelClose"
    >
      <el-button class="mr-2" type="success" link :disabled="isSave" @click="handleChart">
        生成图表
        <el-icon size="18" class="ml-1">
          <DataAnalysis />
        </el-icon>
      </el-button>
    </divider>

    <TableView
      :isSave="isSave"
      ref="tableRef"
      @update:editable="(val) => (isEditableDisabled = val)"
      @update:mask="handelUpdateMask"
    />
  </div>
  <el-empty
    description="当前无报价方式，无法操作"
    v-if="showMask"
    style="height: 100%"
    class="empty-view"
  />
</template>

<script lang="ts" setup>
import TableView from './table.vue'
import divider from '@/views/Bms/components/divider/index.vue'
import { DataAnalysis } from '@element-plus/icons-vue'
import { ref } from 'vue'
const emit = defineEmits(['success'])
const tableRef = ref<InstanceType<typeof TableView> | null>(null)
const isSave = ref(false)
const isEditableDisabled = ref(false)
const showMask = ref(false) // 遮罩控制

const handelUpdateMask = (val: boolean) => {
  showMask.value = val
}

const handleChart = () => {
  tableRef.value?.handleChart()
}

const handelClose = async (fromExternal = false) => {
  await tableRef.value?.reRetchData()
  // 只有手动取消才清除 isSave 状态
  if (!fromExternal) {
    isSave.value = false
  }
}

// 保存处理函数
const handelSave = async () => {
  const isSuccess = await tableRef.value?.handleToggleEdit()

  if (isSuccess) {
    emit('success')
    isSave.value = false
  }
}
</script>

<style lang="scss" scoped>
.empty-view {
  position: relative;
  top: 0;
  left: 0;
  background-color: white;
  z-index: 999;
}
</style>
