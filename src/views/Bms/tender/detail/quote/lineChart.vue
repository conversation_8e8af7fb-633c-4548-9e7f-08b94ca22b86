<template>
  <el-dialog
    v-model="visible"
    width="35%"
    :draggable="true"
    :destroy-on-close="true"
    @close="destroyChart"
  >
    <template #header>
      <strong class="text-4.5">报价趋势折线图 </strong>
    </template>

    <div ref="chartRef" style="width: 100%; height: 400px" v-show="hasChartData"></div>
    <el-empty description="暂无数据" v-show="!hasChartData" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, nextTick, onUnmounted } from 'vue'
import * as echarts from 'echarts'

// 弹窗显示状态
const visible = ref(false)

const destroyChart = () => {
  if (chart) {
    chart.dispose()
    chart = null
  }
}

function mapKeysToLabels(
  rows: Record<string, any>[],
  columns: { label: string; prop: string | number }[]
) {
  const keyMap = columns.reduce(
    (map, col) => {
      if (col.prop !== 'batch') {
        map[col.prop.toString()] = col.label
      }
      return map
    },
    {} as Record<string, string>
  )

  return rows.map((row) => {
    const newRow: Record<string, any> = {}
    for (const [key, value] of Object.entries(row)) {
      if (key === 'batch') {
        newRow.batch = value
      } else {
        newRow[keyMap[key] || key] = value
      }
    }
    return newRow
  })
}
const inputMode = ref(null)
// val 值 tableColumns表头 inputMode报价方式
const open = async (val, tableColumns, mode) => {
  inputMode.value = mode
  visible.value = true
  const data = mapKeysToLabels(mode === 'discount' ? val.dataRow : val.calcData, tableColumns)
  await nextTick()
  await renderChart(data, tableColumns)
}

defineExpose({ open })

// 图表容器
const chartRef = ref<HTMLElement | null>(null)
let chart: echarts.ECharts | null = null
const hasChartData = ref(true)
// 渲染折线图
const renderChart = async (
  data: Record<string, any>[],
  columns: { label: string; prop: string | number }[]
) => {
  await nextTick()
  if (!chartRef.value) return
  if (!chart) {
    chart = echarts.init(chartRef.value)
  }

  // 横轴是三轮报价
  const xAxisLabels = ['第一轮', '第二轮', '第三轮']

  // 提取公司名（跳过 batch）
  const companyNames = columns.filter((c) => c.prop !== 'batch').map((c) => c.label)

  // 构造每家公司报价趋势：从 data 中提取 3 轮数据
  const series = companyNames
    .map((name) => {
      const values = data.map((d) => {
        const val = d[name]
        return val === '' || val === null || val === undefined ? null : parseFloat(val)
      })
      return { name, data: values }
    })
    .filter((item) => item.data.some((v) => v != null)) // ⚠️ 过滤整轮都没有数据的公司
    .map((item) => ({
      name: item.name,
      type: 'line',
      smooth: true,
      data: item.data
    }))
  // ✅ 记录是否有可展示数据
  hasChartData.value = series.length > 0
  chart.setOption({
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        let tooltipText = `${params[0].axisValue}<br/>`
        params.forEach((item: any) => {
          const value = item.data
          const unit = inputMode.value === 'discount' ? '%' : ''
          tooltipText += `${item.marker}${item.seriesName}: ${value != null ? value + unit : '-'}<br/>`
        })
        return tooltipText
      }
    },
    legend: {
      type: 'scroll',
      data: series.map((s) => s.name)
    },
    xAxis: { type: 'category', data: xAxisLabels },
    yAxis: {
      type: 'value',
      name: '报价（单位）',
      min: (value) => Math.floor(value.min - 2),
      max: (value) => Math.ceil(value.max + 2),
      splitNumber: 5,
      axisLine: { lineStyle: { color: '#999' } },
      splitLine: { lineStyle: { color: '#eee', type: 'dashed' } }
    },
    series
  })
}

// 销毁图表实例
onUnmounted(() => {
  // chart?.dispose()
})
</script>
