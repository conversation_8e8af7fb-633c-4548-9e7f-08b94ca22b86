<template>
  <el-drawer
    v-model="dialogVisible"
    :close-on-click-modal="true"
    :close-on-press-escape="false"
    :show-close="false"
    :destroy-on-close="true"
    size="80vw"
  >
    <template #header>
      <div style="width: 36px" class="mt-3 mb-3">
        <div class="drawer-header">
          <div class="drawer-header-title">{{ bidBatchData?.name }}</div>
          <el-button type="info" :icon="Close" circle @click="close" color="#f5f5f5" />
        </div>
      </div>
    </template>

    <template #default>
      <div class="mt-3">
        <div class="dialog-content-view">
          <div class="left-section">
            <div class="left-top">
              <!-- 批次信息 -->
              <BatchInfoPanel
                title="批次信息"
                editable
                :data="bidBatchData"
                :desc-schema="batchInfoDescriptionsSchema"
                :form-schema="batchInfoSchema"
                @batch-info-submit="batchInfoSubmit"
              />
            </div>

            <div class="left-middle">
              <PackageInfoPanel
                title="包信息"
                :selectOptions="bidSubsData"
                :data="currentBidSubsData"
                :desc-schema="packageDescriptionsSchema"
                :form-schema-tip="formSchemaTip(treeDataList, domainData)"
                :form-schema="packageBasicInformation()"
                @bag-info-submit="bagInfoSubmit"
                @bag-change="handleBagChange"
              />
            </div>

            <div class="left-bottom">
              <ProjectInfoPanel
                title="项目信息"
                editable
                :selectOptions="projectsData"
                :data="currentProjectsData"
                :desc-schema="projectDescriptionsSchema"
                :form-schema="projectItemSchema"
                @project-info-submit="projectInfoSubmit"
                @project-change="handleProjectChange"
              />
            </div>
          </div>
          <el-divider direction="vertical" style="height: 100%" />
          <div class="right-section">
            <div class="right-top">
              <quoteView @success="handelQuoteSuccess" />
            </div>
            <div class="right-bottom">
              <standardView :isReset="isStandardReset" @success="Degree++" />
            </div>
          </div>
        </div>
      </div>
    </template>
  </el-drawer>
</template>

<script lang="ts" setup>
import * as Api from '@/api/Bms/tender/index'
import { ref } from 'vue'
import quoteView from './quote/index.vue'
import standardView from './standard/index.vue'
import BatchInfoPanel from './infoPanel/batchInfoPanel.vue'
import PackageInfoPanel from './infoPanel/packageInfoPanel.vue'
import ProjectInfoPanel from './infoPanel/projectInfoPanel.vue'
import useFullScreenLoader from '@/hooks/web/useFullScreenLoader'
import dayjs from 'dayjs'
import { cloneDeep } from 'lodash-es'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'
import { Close } from '@element-plus/icons-vue'
import {
  batchInfoDescriptionsSchema,
  batchInfoSchema,
  packageBasicInformation,
  packageDescriptionsSchema,
  projectDescriptionsSchema,
  projectItemSchema,
  formSchemaTip
} from './data/schema.data'
const useTenderStore = useTenderStoreWithOut()
const dialogVisible = ref(false)
const batchId = ref()
const { withLoader } = useFullScreenLoader()
const emit = defineEmits(['data-updated'])

const bidBatchData = ref() //批次信息
const bidSubsData = ref() //包信息
const projectsData = ref() //项目信息
const allProjectData = ref() // 所有的项目数据下拉
const Degree = ref(0) // 操作计数
const initialDegree = ref(0) // 记录初始 Degree 值

const formatDateToDisplay = (date: any): string => {
  return date ? dayjs(date).format('YYYY-MM-DD') : '-'
}

const isStandardReset = ref(0)
const handelQuoteSuccess = () => {
  isStandardReset.value++
  Degree.value++
}
const getFetch = async () => {
  const { bidBatch, bidSubs, projects } = await Api?.getDetailData(batchId.value)
  await getDomain()

  bidSubs?.forEach((item,index) => {
    item.value = item.id
    item.label = item.bagNum
    item.type = "bidSubs"
    item.index = index
    // 如果 noticeAward 是字符串 "0"，则设置为 null
    if (
      (typeof item.noticeAward === 'string' && item.noticeAward === '0') ||
      item.noticeAward === null ||
      item.noticeAward === undefined
    ) {
      item.noticeAward = null
    } else {
      // 否则转换为数字
      item.noticeAward = Number(item.noticeAward)
    }
  })

  projects?.forEach((item,index) => {
    item.value = item.id
    item.type = "projects"
    item.index = index
    item.label = item.projectName
  })

  if (bidBatch) {
    bidBatchData.value = {
      ...bidBatch,
      // uploadDifficulty:3,
      bidPurchaseDate: formatDateToDisplay(bidBatch.bidPurchaseDate),
      bidOpeningDate: formatDateToDisplay(bidBatch.bidOpeningDate)
    }
  }
  bidSubsData.value = bidSubs
  projectsData.value = projects
  allProjectData.value = projects
}

const open = async (id?: number) => {
  if (!id) return ElMessage({ message: '数据异常', type: 'warning' })
  batchId.value = id
  await withLoader(getFetch())
  dialogVisible.value = true
  initialDegree.value = Degree.value
}

// 统一处理器
const submitWithHandler = async (fn: () => Promise<any>, successMsg: string) => {
  try {
    await withLoader(fn()) //
    ElMessage.success(successMsg)
    await withLoader(getFetch())
    Degree.value++
  } catch (error) {
    ElMessage.error(error instanceof Error ? error.message : '操作失败')
    throw error // 继续向上抛出
  }
}

// 增强版数据转换（保留路径信息）
const transformTreeData = (data: any, parentPath: string[] = []) => {
  return data.map((item) => {
    const currentPath = [...parentPath, item.name]
    return {
      value: item.id.toString(),
      label: item.name,
      rawNode: item,
      path: currentPath, // 这是一个路径数组
      children: item.children ? transformTreeData(item.children, currentPath) : undefined
    }
  })
}

const domainData = ref()
const treeDataList = ref()
const getDomain = async () => {
  const data = await Api.getOptionDomain()
  const treeData = await Api.getServiceTypeTree()
  domainData.value = data
  treeDataList.value = transformTreeData(treeData)
}

// 批次信息提交
const batchInfoSubmit = async (data: any) => {
  data.bidOpeningDate = dayjs(data.bidOpeningDate).valueOf()
  data.bidPurchaseDate = dayjs(data.bidPurchaseDate).valueOf()
  await submitWithHandler(
    () => Api.updateBatch({ ...data, id: batchId?.value }),
    '批次信息更新成功'
  )
}

// 分包信息提交
const bagInfoSubmit = async (data: any) => {
  // 如果字段值是数组（默认 cascader 行为），取最后一个元素
  const finalId = Array.isArray(data.serviceType) ? data.serviceType.slice(-1)[0] : data.serviceType
  await submitWithHandler(
    () =>
      Api.updateSub({
        ...data,
        serviceType: finalId, // 确保提交末级ID
        id: currentBidSubsData.value?.id
      }),
    '分包信息更新成功'
  )
}

// 项目信息提交
const projectInfoSubmit = async (data: any) => {
  await submitWithHandler(
    () =>
      Api.updateProject({
        ...data,
        id: currentProjectsData.value?.id
      }),

    '项目信息更新成功'
  )
}

const close = async () => {
  useTenderStore.setIsClose()
  if (Degree.value > initialDegree.value) emit('data-updated')
  useTenderStore.setBidIndex(0)
  useTenderStore.setProjectsIndex(0)
  dialogVisible.value = false
}

// 全局查找
function findServiceTypePath(serviceTypeId: string, treeData: any[]): string {
  if (!treeData) return '-'

  // 使用广度优先搜索（BFS）查找目标节点及其路径
  const queue: { node: any; path: string[] }[] = treeData.map(node => ({
    node,
    path: [node.label]
  }))

  while (queue.length > 0) {
    const { node, path } = queue.shift()!

    if (node.value === serviceTypeId) {
      return path.join('/')
    }

    if (node.children) {
      for (const child of node.children) {
        queue.push({
          node: child,
          path: [...path, child.label]
        })
      }
    }
  }

  return '-'
}

/* 当前选择的包 */
const currentBidSubsData = ref() // 当前的包信息
const handleBagChange = async (bag: {
  id: number
  quotationMethod: number
  subDomain: Array<number>
  serviceType: string
}) => {
  projectsData.value = cloneDeep(allProjectData.value)?.filter((item) => item?.subId == bag?.id)
  useTenderStore.setServiceType(bag?.serviceType)
  useTenderStore.setQuotationMethod(bag?.quotationMethod)
  useTenderStore.setSubDomain(bag?.subDomain)
  useTenderStore.setBagInfo(bag)

  // 1. 处理 subDomain 中的 id 到 name 的映射
  const subDomainNames = bag?.subDomain
    ?.map((id) => domainData.value.find((option) => Number(option.value) === id)?.label)
    ?.filter((label) => label) // 去除 undefined
    ?.join(', ')
  // 2. 获取 serviceType 的完整路径
  const serviceTypePath = findServiceTypePath(bag?.serviceType?.toString(), treeDataList.value)
  // 2. 设置当前选中的包信息，包含转换后的名称
  currentBidSubsData.value = {
    ...bag,
    serviceTypePath: serviceTypePath,
    companyName: subDomainNames // 或你希望的字段名
  }
}

/* 当前选择的项目 */
const currentProjectsData = ref() // 当前的项目信息
const handleProjectChange = (project: any) => {
  useTenderStore.setProjectInfo(project)
  currentProjectsData.value = project // 存储当前的包信息
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.drawer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
  .drawer-header-title {
    font-weight: 700;
    font-size: 18px;
  }
}

.dialog-content-view {
  height: calc(100vh - 100px);
  overflow: auto;
  display: flex;
  gap: 4px;

  /* 左右分区 */
  .left-section,
  .right-section {
    display: flex;
    flex-direction: column;
  }

  .left-section {
    flex: 6;
    overflow-y: auto;
  }
  .right-section {
    flex: 4;
  }
  .left-middle,
  .left-bottom {
    padding-top: 8px;
  }

  .right-top {
    flex: 4;
  }
  .right-bottom {
    flex: 6;
  }
}
::-webkit-scrollbar {
  display: none;
}
</style>
