.info-panel {
  padding: 4px 0px;
}

/* 统一标签对齐样式 */
:deep(.el-descriptions__label),
:deep(.el-form-item__label) {
  // width: 120px;
  text-align: right;
  padding-right: 12px;
  color: #666;
}
/* 统一标签对齐样式 */
:deep(.right-label-descriptions .el-descriptions__label) {
  width: 120px;
  text-align: right;
  padding-right: 0;
  color: #303133;
}
:deep(.el-form-item__label) {
  text-align: right;
  padding-right: 12px;
  color: #666;
}
/* 全局样式或在当前组件内 */
:deep(.el-descriptions-item__content) {
  color: #17233d;
  white-space: nowrap; /* 禁止换行 */
  overflow: hidden; /* 隐藏溢出内容 */
  text-overflow: ellipsis; /* 显示省略号 */
  max-width: 100px; /* 可选：限制最大宽度 */
  display: inline-block; /* 确保宽度生效 */
}
:deep(.el-input--small) {
  --el-input-height: 30px;
  font-size: 12px;
}
:deep(.el-select--small .el-select__wrapper) {
  min-height: 30px;
  line-height: 30px;
  padding: 5px 10px;
}
:deep(.el-form-item__label-wrap) {
  align-items: center;
}
:deep(.el-descriptions__label) {
  font-weight: 500;
}
:deep(.el-descriptions__cell) {
  padding-bottom: 16px;
}
:deep(.el-descriptions__body .el-descriptions__table:not(.is-bordered) .el-descriptions__cell) {
  padding-bottom: 16px;
}
