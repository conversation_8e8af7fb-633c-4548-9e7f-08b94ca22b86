// 批次信息
export interface BatchInfo {
  batchName: string
  address: string[]
  fileUploadPlatform: string
  uploadStatus: string
  bondAmount: string
  signingDate: string
  purchaseNumber: string
  subBidName: string
}

// 包信息
export interface PackageInfo {
  bagNumber: string
  bagName: string
  purchaser: string
  agent: string
  quoteWay: string
  isNotice: string
  isWin: string
  winCompany: string
}

// 项目信息
export interface ProjectInfo {
  projectName: string
  maxAmount: string
  businessManager: string
  technicalManager: string
  projectNumber: string
  bidFee: string
  winNotice: string
}

// 统一数据结构
export interface UnifiedData {
  batchData: BatchInfo
  packageData: PackageInfo[]
  projectData: ProjectInfo[]
}
