import { useSearchData } from '@/hooks/web/useSearchShareDate'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'

const { setSearchData } = useSearchData()
const width = '230px'

// 筛选项
export const searchSchema = (t, b, u) => [
  {
    field: 'name',
    label: '批次名称',
    component: 'Input',
    componentProps: {
      onChange: setSearchData,
      placeholder: '请输入批次名称（支持模糊搜索）',
      style: { width }
    }
  },
  {
    field: 'region',
    label: '地区',
    component: 'Input',
    componentProps: {
      onChange: setSearchData,
      style: { width }
    }
  },

  {
    field: 'bidOpeningDateRange',
    label: '开标日期',
    component: 'DatePicker',
    componentProps: {
      onChange: setSearchData,
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      style: { width: '180px' }
    }
  },

  {
    field: 'procurementNumber',
    label: '采购编号',
    component: 'Input',
    componentProps: {
      onChange: setSearchData,
      style: { width }
    }
  },

  {
    field: 'subDomain',
    label: '参与公司',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      options: getIntDictOptions(DICT_TYPE.BMS_DOMAIN),
      filterable: true,
      // multiple: true,
      // collapseTags: true,
      // maxCollapseTags: 1,
      style: { width }
    }
  },
  {
    field: 'subType',
    label: '主/竞标',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      options: getIntDictOptions(DICT_TYPE.BMS_SUB_TYPE),
      filterable: true,
      style: { width }
    }
  },
  // {
  //   field: 'subType',
  //   label: '是否异地评标',
  //   component: 'Select',
  //   componentProps: {
  //     onChange: setSearchData,
  //     options: getIntDictOptions(DICT_TYPE.BMS_SUB_TYPE),
  //     filterable: true,
  //     style: { width }
  //   }
  // },
  {
    field: 'businessDirector',
    label: '商务负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: b,
      style: { width }
    }
  },

  {
    field: 'technicalDirector',
    label: '技术负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: t,
      style: { width }
    }
  },

  {
    field: 'uploadDirector',
    label: '上传负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: u,
      style: { width }
    }
  }
]

// 表格项
export const tableColumns = [
  {
    label: '地区',
    field: 'region',
    formatter: (row: { region: string }) => row.region || '--',
    fixed: 'left',
    width: 80
  },
  {
    label: '批次名称',
    field: 'name',
    fixed: 'left',
    showOverflowTooltip: false,
    width: 160
  },
  {
    label: '属性',
    field: 'property',
    showOverflowTooltip: false,
    formatter: (row: { region: string }) => row.region || '--',
    width: 60
  },
  {
    label: '买标截止日期',
    field: 'bidPurchaseDate',
    showOverflowTooltip: false,
    // formatter: (row: { bidPurchaseDate: string }) =>
    //   row.bidPurchaseDate ? dayjs(row.bidPurchaseDate).format('YYYY-MM-DD') : '--',
    width: 70
  },
  {
    label: '开标日期',
    field: 'bidOpeningDate',
    showOverflowTooltip: false,
    width: 60
  },
  {
    label: '采购编号',
    field: 'procurementNumber',
    showOverflowTooltip: false,
    formatter: (row: { procurementNumber: string }) => row.procurementNumber || '--',
    width: 55
  },
  {
    label: '采购人',
    field: 'purchaser',
    showOverflowTooltip: false,
    formatter: (row: { purchaser: string }) => row.purchaser || '--',
    width: 55
  },

  // {
  //   label: '上传难度',
  //   field: 'difficulty',
  //   showOverflowTooltip: false,
  //   formatter: (row: { uploadDifficulty: number }) => {
  //     const dictOption = getIntDictOptions(DICT_TYPE.BMS_UPLOAD_DIFFICULTY).find(
  //       (item) => item.value === row.uploadDifficulty
  //     )
  //     return dictOption?.label || '--'
  //   },
  //   width: 60
  // },

  {
    label: '代理机构',
    field: 'institutionName',
    showOverflowTooltip: false,
    formatter: (row: { institutionName: string }) => row.institutionName || '--',
    width: 90
  },
  {
    label: '代理机构电话',
    field: 'institutionTel',
    showOverflowTooltip: false,
    formatter: (row: { institutionTel: string }) => row.institutionTel || '--',
    width: 120
  },
  {
    label: '招标代理费',
    field: 'agencyFee',
    showOverflowTooltip: false,
    formatter: (row: { agencyFee: string }) => row.agencyFee || '--',
    width: 70
  },
  {
    label: '是否已交',
    field: 'isPay',
    formatter: (row: { isPay: number }) => {
      const dictOption = getIntDictOptions(DICT_TYPE.BMS_IS_PAY).find(
        (item) => item.value === row.isPay
      )
      return dictOption?.label || '--'
    },
    minWidth: 55
  },

  {
    label: '分标编号',
    field: 'subBidNumberMerge',
    showOverflowTooltip: false,
    width: 200
  },
  {
    label: '分标名称',
    field: 'subBidNameMerge',
    showOverflowTooltip: false,
    minWidth: 150
  },
  {
    label: '主/竞标',
    field: 'subTypeMerge',
    showOverflowTooltip: false,
    minWidth: 75
  },
  // {
  //   label: '是否中标',
  //   field: 'isBid',
  //   formatter: (row: { isBid: number }) => row.isBid || '--',
  //   minWidth: 100
  // },
  // {
  //   label: '中标单位',
  //   field: 'winningBidder',
  //   minWidth: 120
  // },

  {
    label: '包号',
    field: 'bagNumMerge',
    showOverflowTooltip: false,
    width: 60
  },
  {
    label: '包名称',
    field: 'bagNameMerge',
    showOverflowTooltip: false,
    width: 280
  },
  {
  label: '包数',
  field: 'bagCount',
  showOverflowTooltip: false,
  width: 60
  },
  {
    label: '保证金(万)',
    field: 'marginMerge',
    showOverflowTooltip: false,
    minWidth: 75
  },
  // {
  //   label: '项目名称',
  //   field: 'projectNameMerge',
  //   showOverflowTooltip: false,
  //   minWidth: 240
  // },
  {
    label: '预算金额',
    field: 'budgetAmountMerge',
    showOverflowTooltip: false,
    width: 90
  },
  {
    label: '最高限额',
    field: 'maximumLimitMerge',
    showOverflowTooltip: false,
    width: 90
  },
  {
    label: '参与公司及报价',
    field: 'pid2Quotation',
    showOverflowTooltip: false,
    minWidth: 240
  },

  {
    label: '报价方式',
    field: 'quotationMethodMerge',
    showOverflowTooltip: false,
    width: 95
  },
  {
    label: '商务负责人',
    field: 'businessDirectorMerge',
    showOverflowTooltip: false,
    width: 110
  },
  {
    label: '技术负责人',
    field: 'technicalDirectorMerge',
    showOverflowTooltip: false,
    width: 110
  },
  {
    label: '上传负责人',
    field: 'uploadDirectorMerge',
    showOverflowTooltip: false,
    width: 110
  }
]

/*
 * 项目信息需要 合并 && 自定义slot 的字段
 * 第一个为最大单位 作为高度的参照
 */
export const projectMergeColumns = [
  { slot: 'maximumLimitMerge', field: 'maximumLimitMerge' },
  { slot: 'budgetAmountMerge', field: 'budgetAmountMerge' },
  { slot: 'projectOverview', field: 'projectOverview' },
  { slot: 'projectUnit', field: 'projectUnit' },
  { slot: 'requirements', field: 'requirements' },
  { slot: 'pid2Quotation', field: 'pid2Quotation' }
]

/*
 * 参与公司及报价 单独维护
 */
export const SUB_DOMAIN_FIELDS = 'pid2Quotation'

/*
 * 包信息需要 合并 && 自定义slot 的字段
 */
export const bagMergeColums = [
  { slot: 'winningBidder', field: 'winningBidder' },
  { slot: 'marginMerge', field: 'marginMerge' },
  { slot: 'bagNumMerge', field: 'bagNumMerge' },
  { slot: 'bagNameMerge', field: 'bagNameMerge' },
  { slot: 'technicalDirectorMerge', field: 'technicalDirectorMerge' },
  { slot: 'uploadDirectorMerge', field: 'uploadDirectorMerge' },
  { slot: 'subBidNumberMerge', field: 'subBidNumberMerge' },
  { slot: 'subBidNameMerge', field: 'subBidNameMerge' },
  { slot: 'quotationMethodMerge', field: 'quotationMethodMerge' },
  { slot: 'weightMerge', field: 'weightMerge' },
  { slot: 'businessDirectorMerge', field: 'businessDirectorMerge' },
  { slot: 'subTypeMerge', field: 'subTypeMerge' }
]

export const BAG_MERGE_FIELDS = bagMergeColums.map((col) => col.field)

export const PROJECT_MERGE_FIELDS = projectMergeColumns.map((col) => col.field)
