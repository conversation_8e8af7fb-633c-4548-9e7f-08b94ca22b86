// composables/useBidDateUtils.ts
import dayjs from 'dayjs'

/**
 * 判断当前日期是否在开标日期前两周内（含当天）
 * @param bidOpeningDate 开标日期（字符串或 dayjs 支持的格式）
 * @returns boolean
 */
function isWithinTwoWeeksBeforeBid(bidOpeningDate: string | Date | dayjs.Dayjs): boolean {
  if (!bidOpeningDate) return false

  const today = dayjs().startOf('day')
  const bidDate = dayjs(bidOpeningDate).startOf('day')
  const twoWeeksBeforeBid = bidDate.subtract(2, 'week')

  const isAfterTwoWeeksBefore = today.diff(twoWeeksBeforeBid, 'day') >= 0
  const isBeforeBidDate = today.diff(bidDate, 'day') <= 0

  return isAfterTwoWeeksBefore && isBeforeBidDate
}

/**
 * 判断开标截止日期是否在今天、前1天或前2天（即是否“即将截止”）
 * @param bidPurchaseDate 截止日期
 * @returns boolean
 */
function isDateExpired(bidPurchaseDate: string | Date | dayjs.Dayjs): boolean {
  if (!bidPurchaseDate) return false

  const today = dayjs().startOf('day')
  const bidDate = dayjs(bidPurchaseDate).startOf('day')
  const diffDays = bidDate.diff(today, 'day')

  return diffDays >= 0 && diffDays <= 2
}

export function useBidDateUtils() {
  return {
    isWithinTwoWeeksBeforeBid,
    isDateExpired
  }
}
