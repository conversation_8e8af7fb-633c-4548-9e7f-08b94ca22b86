// useOption.ts
import { ref } from 'vue'
import * as Api from '@/api/Bms/tender/index'

export interface OptionItem {
  label: string
  value: string
}

export function useOption() {
  const tList = ref<OptionItem[]>([])
  const uList = ref<OptionItem[]>([])
  const bList = ref<OptionItem[]>([])
  const isLoaded = ref(false)

  const toOptions = (list: (string | null)[]): OptionItem[] => {
    if (!Array.isArray(list)) return []
    return list.filter(Boolean).map((item) => ({
      label: item!,
      value: item!
    }))
  }

  const fetchOptions = async () => {
    const data = await Api.getOptionsData()
    const { blist, tlist, ulist } = data

    bList.value = toOptions(blist)
    tList.value = toOptions(tlist)
    uList.value = toOptions(ulist)
    isLoaded.value = true
  }

  const resetOptions = async () => {
    isLoaded.value = false
    await fetchOptions()
  }

  return {
    tList,
    uList,
    bList,
    isLoaded,
    fetchOptions,
    resetOptions
  }
}