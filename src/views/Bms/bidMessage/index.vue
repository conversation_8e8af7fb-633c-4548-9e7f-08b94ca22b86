<template>
  <ContentWrap>
    <span v-show="false"> {{ searchRef?.offsetHeight }}</span>
    <div ref="searchRef">
      <el-collapse v-model="activeNames">
        <el-collapse-item name="search">
          <Search
            :schema="searchSchema(t, u, b, h)"
            @reset="handleEvent('search-reset')"
            @search="setSearchParams"
          />
        </el-collapse-item>
      </el-collapse>
    </div>
  </ContentWrap>
  <el-card shadow="never">
    <div ref="tableTopRef">
      <template v-if="!multipleSelection.length">
        <!-- v-if="checkRole(['bms_sub_hit_n1_role'])" -->
        <el-button type="primary" plain @click="handleEvent('import')"> 导入 </el-button>
        <el-button type="warning" plain @click="handleEvent('template-download')">
          模板下载
        </el-button>
      </template>

      <el-button
        v-if="multipleSelection.length"
        plain
        type="warning"
        @click="handleEvent('remark')"
      >
        标讯确认
      </el-button>

      <Count
        v-if="multipleSelection?.length"
        class="mt-2"
        :page-size="total"
        :selected-length="multipleSelection?.length"
        @rest="() => multipleTableRef!.clearSelection()"
      />

      <div class="table-container mt-3">
        <el-table
          v-loading="tableLoading"
          ref="multipleTableRef"
          :max-height="formatterTableHeight"
          :data="tableData"
          :span-method="objectSpanMethod"
          :border="true"
          row-key="id"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column
            type="selection"
            :selectable="(row) => !row?.disabled"
            width="55"
            :align="'center'"
            :fixed="'left'"
          />

          <el-table-column
            v-for="col in columns"
            :align="'center'"
            :key="col.field"
            :prop="col.field"
            :label="col.label"
            :width="col?.width || 'auto'"
            :min-width="col?.minWidth || 'auto'"
            :fixed="col?.fixed"
            :formatter="formatter"
          >
            <template #header="{ column }">
              <div class="tooltip-container" v-if="col.field === 'records'">
                <span> {{ column?.label }}</span>
                <el-tooltip content="商务组 -> 业务组 -> 投标组" placement="top" width="210">
                  <el-icon><InfoFilled /></el-icon>
                </el-tooltip>
              </div>
            </template>

            <template #default="{ row }">
              <template v-if="col.field === 'records'">
                <el-steps :active="row?.records?.currentState" finish-status="success" align-center>
                  <el-step
                    v-for="(_item, index) in row?.records?.records"
                    :key="index"
                    class="step-item"
                    @click="handelStepClick(_item)"
                  >
                    <template #icon>
                      <i
                        class="iconfont icon-active-circle"
                        :style="{
                          fontSize: '12px',
                          color: index < row?.records?.currentState ? '#67C23A' : '#F56C6C'
                        }"
                      ></i>
                    </template>
                  </el-step>
                </el-steps>
              </template>

              <template v-if="col.field === 'bidPurchaseDate'">
                <span
                  v-if="row.bidPurchaseDate"
                  :class="{ 'expired-date': isDateExpired(row.bidPurchaseDate) }"
                >
                  {{ dayjs(row.bidPurchaseDate).format('YYYY-MM-DD') }}
                </span>
                <span v-else>--</span>
              </template>

              <template v-if="col.field === 'bidOpeningDate'">
                <span
                  v-if="row.bidOpeningDate"
                  :class="{ 'expired-date': isWithinTwoWeeksBeforeBid(row.bidOpeningDate) }"
                >
                  {{ dayjs(row.bidOpeningDate).format('YYYY-MM-DD') }}
                </span>
                <span v-else>--</span>
              </template>

              <template v-if="projectMergeFields.includes(col.field)">
                <div class="project-merge-view">
                  <div
                    v-for="(item, index) in row[col.field]"
                    :key="`${row.id}-${col.field}-${index}`"
                    class="project-merge-item"
                  >
                    <el-tooltip
                      v-if="getOverflowFlag(row.id, col.field, index)"
                      :content="item || '--'"
                      placement="top"
                    >
                      <span
                        :ref="(el) => el && setTextRef(el, row.id, col.field, index)"
                        class="project-merge-item-content"
                      >
                        {{ item || '--' }}
                      </span>
                    </el-tooltip>
                    <span
                      v-else
                      :ref="(el) => el && setTextRef(el, row.id, col.field, index)"
                      class="project-merge-item-content"
                    >
                      {{ item || '--' }}
                    </span>
                    <el-divider v-if="index < row[col.field].length - 1" style="margin: 2px 0" />
                  </div>
                </div>
              </template>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-pagination
        v-model:current-page="pageParams.pageNo"
        v-model:page-size="pageParams.pageSize"
        @change="handelPageChange"
        layout="total, sizes, prev, pager, next, jumper"
        background
        :total
        class="float-right mb-15px mt-15px"
      />
    </div>
  </el-card>
  <RemarkView ref="remarkRef" @success="() => getTableData()" />
  <AlertRemark ref="alertRemarkRef" />
  <ImportView ref="importRef" uploadUrl="/bms/bid/import" @success="() => getTableData()" />
</template>

<script setup lang="ts">
import * as Api from '@/api/Bms/bidMessage/index'
import { useBidDateUtils } from '@/views/Bms/hooks/useBidDateUtils'
import { InfoFilled } from '@element-plus/icons-vue'
import { ElPagination } from 'element-plus'
import type { TableInstance } from 'element-plus'
import { checkRole } from '@/utils/permission'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'

import { DICT_TYPE, getDictLabel } from '@/utils/dict'
import download from '@/utils/download'
import { cleanEmptyKeys } from '@/utils/formatter'
import { columns, bidBatchMergeFields, searchSchema, projectMergeFields } from './bidMessage.data'
import { useElementHeight } from '@/hooks/web/useDomHeight'
import { useOptionStoreWithOut } from '@/store/modules/bms/useOptionStore'

import Count from '@/views/Bms/components/count/index.vue'
import ImportView from '../components/import/index.vue'
import RemarkView from './remark/index.vue'
import AlertRemark from './detail/alertRemark.vue'

const { isWithinTwoWeeksBeforeBid, isDateExpired } = useBidDateUtils()
const optionStore = useOptionStoreWithOut()

const activeNames = ref('search')
const remarkRef = ref()
const searchRef = ref()
const importRef = ref()
const alertRemarkRef = ref()
const tableData: any = ref([])
const total = ref(0)
const tableLoading = ref(false)
const searchParams = ref()
const multipleTableRef = ref<TableInstance>()
const multipleSelection = ref([])

const { domRef: tableTopRef, domHeight: tableTopDomHeight } = useElementHeight()

const multipleSelectionId = computed(() =>
  multipleSelection.value.map((item: { id: number }) => Number(item.id))
)
/*
 * 动态计算表格高度
 */
const formatterTableHeight = computed(() => {
  const searchHeight = activeNames.value.includes('search')
    ? (searchRef.value?.offsetHeight || 0)
    : 0;

  const topHeight = tableTopDomHeight.value;
  const selectionOffset = multipleSelection.value.length ? 24 : 0;
  const fixedOffset = 100; // 其他固定高度元素的总和

  // 计算可用高度，确保不小于最小高度
  const availableHeight = window.innerHeight - (searchHeight + topHeight + selectionOffset + fixedOffset);
  const minHeight = 300;

  return `${Math.max(availableHeight, minHeight)}px`;
});
// const formatterTableHeight = computed(() => {
//   return `calc(100vh - ${(searchRef.value?.offsetHeight ?? 0) + (multipleSelection.value.length ? 24 : 0) + tableTopDomHeight.value + 130}px)`
// })

const pageParams = ref({
  pageSize: 50,
  pageNo: 1
})

const { tableMethods } = useTable({
  getListApi: async (option) => {
    if (option?.bidOpeningDateRange?.length === 2) {
      const [start, end] = option.bidOpeningDateRange
      option.bidOpeningDateStart = start
      option.bidOpeningDateEnd = end
      delete option.bidOpeningDateRange
    }
    // 过滤掉原有组件内分页相关字段
    const { pageNo, pageSize, ...restOption } = option
    searchParams.value = restOption
    await getTableData()
  }
})

const { setSearchParams } = tableMethods
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

/*
 * 分页变化
 */
const handelPageChange = () => {
  getTableData()
}

/*
 * 点击步骤条 打开弹窗
 */
const handelStepClick = (row) => {
  if (!row?.operator) return ElMessage.warning('暂无信息！')
  alertRemarkRef.value.openDialog(row)
}

/*
 * 合并单元格
 */
const objectSpanMethod = ({ row, column, rowIndex, columnIndex }) => {
  // 如果是选择列(第一列)，不进行合并
  if (columnIndex === 0) {
    return {
      rowspan: 1,
      colspan: 1
    }
  }
  // 获取所有相同batchId的行数
  const getBidIdSpan = (batchId) => {
    return tableData.value.filter((item) => item.batchId === batchId).length
  }
  // 获取当前行的起始位置
  const getStartIndex = (batchId) => {
    return tableData.value.findIndex((item) => item.batchId === batchId)
  }

  // 获取当前列的字段名
  const columnFields = columns.map((col) => col.field)
  const currentField = columnFields[columnIndex - 1] // 因为第一列是选择框，所以需要减1
  // 判断当前列是否需要合并
  if (currentField && bidBatchMergeFields.includes(currentField)) {
    const batchId = row.batchId
    const startIndex = getStartIndex(batchId)
    const span = getBidIdSpan(batchId)

    if (rowIndex === startIndex) {
      return {
        rowspan: span,
        colspan: 1
      }
    } else {
      return {
        rowspan: 0,
        colspan: 0
      }
    }
  }

  // 不需要合并的列返回默认值
  return {
    rowspan: 1,
    colspan: 1
  }
}

/*
 * 操作事件
 */
const handleEvent = async (type) => {
  switch (type) {
    case 'remark':
      remarkRef.value.openDialog(multipleSelectionId.value)
      break

    case 'search-reset':
      searchParams.value = {}
      pageParams.value = {
        pageSize: 50,
        pageNo: 1
      }
      getTableData()
      break

    case 'import':
      importRef.value?.open()
      break

    case 'template-download':
      const data = await Api.templateDownload()
      download.common(data)
      break
    default:
      console.error('未知类型')
      break
  }
}

/*
 * 获取表格数据
 */
const count = ref(0)
const getTableData = async () => {
  try {
    tableLoading.value = true
    const cloneSearchParams = cloneDeep(searchParams.value)

    const ignore =
      cloneSearchParams?.ignore === undefined || cloneSearchParams?.ignore === ''
        ? true
        : cloneSearchParams?.ignore

    // 合并分页参数和搜索参数 过滤空值
    const params = cleanEmptyKeys(
      {
        ...pageParams?.value,
        ...cloneSearchParams,
        ignore
      },
      { removeEmptyString: true },
      ['ignore']
    )
    const data = await Api.getBidTableList(params)
    const cloneData = cloneDeep(data)
    // 处理数据: bidBatch内的字段 提到外层
    const processedTableData = cloneData.list?.map((item) => {
      const extracted = {}
      bidBatchMergeFields.forEach((field) => {
        // 优先使用外层字段,如果外层没有再使用bidBatch中的字段
        extracted[field] = item[field] ?? item.bidBatch?.[field] ?? null
      })
      return {
        ...item,
        ...extracted
      }
    })
    if (count.value) multipleTableRef?.value?.clearSelection()
    count.value++

    tableData.value = processedTableData
    total.value = data.total
  } finally {
    tableLoading.value = false
  }
}

/*
 * 表格数据格式化
 */
const formatter = (row, column) => {
  if (column.property === 'businessType')
    return getDictLabel(DICT_TYPE.BMS_BITCH_TYPE, row[column.property]) || '--'
  if (column.property === 'subType')
    return getDictLabel(DICT_TYPE.BMS_SUB_TYPE, row[column.property]) || '--'
  if (column.property === 'quotationMethod')
    return getDictLabel(DICT_TYPE.BMS_QUOTATION_METHOD, row[column.property]) || '--'
  if (column.property === 'bidOpeningDate')
    return row[column.property] ? dayjs(row[column.property]).format('YYYY-MM-DD') : '--'
  if (column.property === 'bidPurchaseDate')
    return row[column.property] ? dayjs(row[column.property]).format('YYYY-MM-DD') : '--'
  return row[column.property] || '--'
}

// 使用对象存储每个元素的引用和溢出状态
const textBagsRefs = ref<Record<string, HTMLElement>>({})
const overflowFlags = ref<Record<string, boolean>>({})
// 设置文本引用的函数，使用唯一键
const setTextRef = (el: any, rowId: string, field: string, index: number) => {
  const key = `${rowId}-${field}-${index}`
  textBagsRefs.value[key] = el
}
// 获取溢出状态
const getOverflowFlag = (rowId: string, field: string, index: number) => {
  const key = `${rowId}-${field}-${index}`
  return overflowFlags.value[key] || false
}

// 检查每个条目的溢出状态
const checkOverflow = () => {
  nextTick(() => {
    setTimeout(() => {
      const newOverflowFlags: Record<string, boolean> = {}
      Object.keys(textBagsRefs.value).forEach((key) => {
        const el = textBagsRefs.value[key]
        newOverflowFlags[key] = el && el.scrollWidth > el.clientWidth
      })
      overflowFlags.value = newOverflowFlags
    }, 0)
  })
}

/*
 * 搜索框高度变化
 */
const updateHeight = () => {
  if (searchRef.value) {
    searchRef.value = searchRef.value.offsetHeight
  }
}

// 监听 列表宽度 变化
const resizeTableObservers: ResizeObserver[] = []
const observeAllCells = () => {
  const elements = document.querySelectorAll('.project-merge-view')
  if (elements) {
    const observer = new ResizeObserver(() => {
      checkOverflow()
    })
    elements.forEach((element) => observer.observe(element))
    resizeTableObservers.push(observer)
  }
}

let resizeObserver
interface OptionItem {
  label: string
  value: string
}
const t = ref<OptionItem[]>([]) // 技术负责人
const u = ref<OptionItem[]>([]) // 上传负责人
const b = ref<OptionItem[]>([]) // 商务负责人
const h = ref<OptionItem[]>([]) // 状态下拉
onMounted(async () => {
  await nextTick()
  await getTableData()
  const data = await Api.getHitStatusOption()
  h.value = data
  t.value = optionStore.getTList
  u.value = optionStore.getUList
  b.value = optionStore.getBList

  resizeObserver = new ResizeObserver(updateHeight)
  observeAllCells()
  if (searchRef.value) {
    resizeObserver.observe(searchRef.value)
    resizeTableObservers.push(resizeObserver)
  }
})

onUnmounted(() => {
  if (resizeObserver && searchRef.value) {
    resizeObserver.unobserve(searchRef.value)
  }
  resizeTableObservers.forEach((observer) => observer.disconnect())
})
</script>

<style lang="scss" scoped>
@import url(@/styles/collapse.scss);
.project-merge-view {
  display: flex;
  flex-direction: column;
}
.project-merge-item {
  display: flex;
  flex-direction: column;
}
.project-merge-item-content {
  // 超出不换行展示省略号
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.expired-date {
  color: red;
  font-weight: bold;
}
:deep(.el-step__icon) {
  width: 0px;
}
.tooltip-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  .tooltip-content {
    white-space: nowrap;
  }
}

/* 分割线：左右超出 12px */
:deep(.el-divider) {
  width: calc(100% + 24px);
  transform: translateX(-12px);
}

:deep(.el-step) {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.step-item {
  & > :hover {
    scale: 1.06;
    cursor: pointer;
  }
  transition: all 0.3s;
}
</style>
