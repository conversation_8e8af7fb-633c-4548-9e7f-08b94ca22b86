import { useSearchData } from '@/hooks/web/useSearchShareDate'
import { DICT_TYPE, getDictOptions } from '@/utils/dict'

const { setSearchData } = useSearchData()

const width = '200px'

// 筛选项
export const searchSchema = (t, u, b, h) => [
  {
    field: 'region',
    label: '地区',
    component: 'Input',
    componentProps: {
      onChange: setSearchData,
      style: { width }
    }
  },

  {
    field: 'bidOpeningDateRange',
    label: '开标日期',
    component: 'DatePicker',
    componentProps: {
      onChange: setSearchData,
      type: 'daterange',
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      style: { width: '180px' }
    }
  },

  {
    field: 'procurementNumber',
    label: '采购编号',
    component: 'Input',
    componentProps: {
      onChange: setSearchData,
      style: { width }
    }
  },

  {
    field: 'businessDirector',
    label: '商务负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: b,
      style: { width }
    }
  },

  {
    field: 'technicalDirector',
    label: '技术负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: t,
      style: { width }
    }
  },

  {
    field: 'projectName',
    label: '上传负责人',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: u,
      style: { width }
    }
  },
  {
    field: 'states',
    label: '状态',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      multiple: true,
      collapseTags: true,
      maxCollapseTags: 1,
      options: h,
      style: { width }
    }
  },
  {
    field: 'businessType',
    label: '业务类型',
    component: 'Select',
    componentProps: {
      onChange: setSearchData,
      filterable: true,
      options: getDictOptions(DICT_TYPE.BMS_BITCH_TYPE),
      style: { width }
    }
  },

  {
    label: '',
    field: 'ignore',
    component: 'Switch',
    componentProps: {
      onChange: setSearchData,
      defaultValue: true,
      activeValue: true,
      inactiveValue: false,
      activeText: '开启过滤',
      inactiveText: '否'
    }
  }
]

export const columns = [
  { field: 'region', label: '地区', width: 80, fixed: 'left' },
  { field: 'name', label: '批次名称', width: 160, fixed: 'left' },
  { field: 'property', label: '属性', minWidth: 60, fixed: 'left' },
  { field: 'bidPurchaseDate', label: '买标截止日期', minWidth: 70 },
  { field: 'bidOpeningDate', label: '开标日期', minWidth: 60 },
  { field: 'procurementNumber', label: '采购编号', minWidth: 55 },
  { field: 'purchaser', label: '采购人', minWidth: 80 },
  { field: 'institutionName', label: '代理机构', minWidth: 90 },
  { field: 'institutionTel', label: '代理机构电话', minWidth: 120 },
  { field: 'agencyFee', label: '招标代理费', minWidth: 70 },

  { field: 'requirements', label: '资质要求', minWidth: 82 },
  { field: 'personnel', label: '人员要求', minWidth: 82 },
  { field: 'performance', label: '业绩标准', minWidth: 82 },

  { field: 'businessType', label: '业务类型', width: 82 },
  { field: 'subBidNumber', label: '分标编号', minWidth: 200 },
  { field: 'subBidName', label: '分标名称', width: 150 },
  { field: 'subType', label: '主/竞标', width: 75 },
  { field: 'bagNum', label: '包号', width: 60 },
  { field: 'bagName', label: '包名称', minWidth: 280 },
  { field: 'margin', label: '保证金', width: 75 },
  { field: 'projectNameMerge', label: '项目名称', minWidth: 240 },
  { field: 'budgetAmountMerge', label: '预算金额', minWidth: 90 },
  { field: 'maximumLimitMerge', label: '最高限额', width: 90 },
  { field: 'quotationMethod', label: '报价方式', width: '100px' },
  { field: 'businessDirector', label: '商务负责人', width: '100px' },
  { field: 'technicalDirectorMerge', label: '技术负责人', width: '100px' },
  { field: 'records', label: '流程阶段', width: '160px', fixed: 'right' }
]

/*
 * 项目相关的字段 自动合并
 */
export const projectMergeFields = ['maximumLimitMerge', 'projectNameMerge', 'budgetAmountMerge']

/*
 * 批次相关的字段 自动合并
 */
export const bidBatchMergeFields = [
  'name', // 批次名称
  'platform', // 采购平台
  'region', // 地区
  'procurementNumber', // 采购编号
  'property', // 属性
  'purchaser', // 采购人
  'agencyFee', // 招标代理费
  'institutionName', // 代理机构
  'institutionTel', // 代理机构电话
  'bidOpeningDate', // 开标日期
  'bidPurchaseDate' // 买标截止日期
]
