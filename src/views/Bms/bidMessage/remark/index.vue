<template>
  <div class="remark-container">
    <el-dialog
      v-model="dialogVisible"
      title="备注"
      width="30%"
      :draggable="true"
      :before-close="handleClose"
    >
      <el-input v-model="records" type="textarea" :rows="8" placeholder="请输入备注内容" />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import useFullScreenLoader from '@/hooks/web/useFullScreenLoader'
import * as Api from '@/api/Bms/bidMessage/index'

const emit = defineEmits(['success'])
const dialogVisible = ref(false)
const records = ref()

const { withLoader } = useFullScreenLoader()

const handleClose = () => {
  dialogVisible.value = false
  records.value = ''
}
const bidIds = ref<number[]>([])
const handleSubmit = async () => {
  // if (!records.value?.trim()) return ElMessage.error('请输入备注内容')
  await withLoader(Api.hitRecords({ records: records.value, bidIds: bidIds.value }))
  ElMessage.success('备注成功')
  emit('success')
  handleClose()
}

// 定义打开弹窗的方法
const openDialog = (multipleSelectionId: number[]) => {
  bidIds.value = multipleSelectionId
  dialogVisible.value = true
}

// 暴露组件方法供外部使用
defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  border-radius: 12px;
}
</style>
