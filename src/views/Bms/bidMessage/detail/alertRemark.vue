<template>
  <div class="remark-container">
    <el-dialog v-model="dialogVisible" width="26%" :draggable="true" class="custom-remark-dialog">
      <div class="remark-content">
        <div class="info-line">
          <span class="label">操作人：</span>
          <span class="value">{{ data?.operatorName || '暂无' }}</span>
        </div>
        <div class="info-line">
          <span class="label">操作时间：</span>
          <span class="value">{{
            data?.createTime ? dayjs(data?.createTime).format('YYYY-MM-DD HH:mm:ss') : '暂无'
          }}</span>
        </div>
        <div class="info-line">
          <span class="label">备注：</span>
          <span class="remark-box">
            {{ data?.records || '暂无' }}
          </span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import dayjs from 'dayjs'
const emit = defineEmits(['success'])
const dialogVisible = ref(false)

const data = ref()
// 定义打开弹窗的方法
const openDialog = (val) => {
  data.value = val
  dialogVisible.value = true
}

// 暴露组件方法供外部使用
defineExpose({
  openDialog
})
</script>

<style scoped lang="scss">
:deep(.el-dialog) {
  border-radius: 12px;
}
.remark-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
  font-size: 15px;
}
.info-line {
  .label {
    font-size: 15px;
    font-weight: 500;
  }
}
</style>
