<template>
  <div class="mb-2 text-[14px]">共计&nbsp;{{ props.pageSize }}&nbsp;项&nbsp;&nbsp;已选中
    <span class="color-[#2d5cf6]">{{props.selectedLength}}</span> 项&nbsp;
    <span class="color-[#2d5cf6] cursor-pointer" @click="handelClearSelection">重置</span>
  </div>
</template>
<script setup lang="ts">
defineOptions({name: 'SystemCount'})
import {defineProps} from 'vue';
const emit = defineEmits(['rest'])
const props = defineProps({
  pageSize:{
    type:Number,
    default: 0
  },
  selectedLength: {
    type: Number,
    default: 0
  }
})
const handelClearSelection = () => {
  emit('rest')
}
</script>
