<script setup lang="ts">
defineOptions({ name: 'BatchDivider' })
import { ref } from 'vue'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'
import * as _ from 'lodash-es'
const emit = defineEmits(['update:isSave', 'handelSave', 'packageChange', 'close'])

const isSaveLocal = computed({
  get: () => props.isSave,
  set: (val) => emit('update:isSave', val)
})

const useTenderStore = useTenderStoreWithOut()
const isClose = computed(() => useTenderStore.getIsClose)

const props = defineProps({
  selectData: {
    type: [Array, Object] as PropType<
      Array<{ label: string; value: string | number }> | Record<string, any>
    >,
    default: () => []
  },
  isSave: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '标题'
  },
  isDisabled: {
    type: Boolean,
    default: false
  },
  showCancel: {
    // 新增 props，控制是否显示取消按钮
    type: Boolean,
    default: true
  }
})

const cloneData = computed(() => _.cloneDeep(props.selectData))
const currentPackage: any = ref()

// 包变更处理
const handlePackageChange = (val: any) => {
  if (_.isEqual(currentPackage.value, val)) return // 避免重复触发
  if (val.type === "bidSubs") useTenderStore.setBidIndex(val?.index)
  if (val.type === "projects") useTenderStore.setProjectsIndex(val?.index)

  currentPackage.value = val
  emit('packageChange', val)
}

const handelUpdateOrSave = () => {
  isSaveLocal.value ? emit('handelSave') : (isSaveLocal.value = true)
}

// 取消方法
const handleCancel = () => {
  emit('close')
}

watch(
  () => cloneData.value,
  (newVal) => {
    if (!Array.isArray(newVal) || newVal.length === 0) return;
    const { getBidIndex = 0, getProjectsIndex = 0 } = useTenderStore;
    const firstItem = newVal[0];
    const targetIndex = firstItem.type === 'bidSubs' ? getBidIndex : getProjectsIndex;
    const targetItem = newVal[targetIndex] ?? newVal[0];
    if (newVal.length > 0 || !_.isEqual(currentPackage.value, targetItem)) {
      currentPackage.value = targetItem;
      emit('packageChange', targetItem);
    }
  },
  { immediate: true }
)

watch(
  () => isClose.value,
  (newVal) => {
    if (newVal) {
      isSaveLocal.value = false // 重置保存状态
      currentPackage.value = null // 重置当前包
    }
  }
)
</script>

<template>
  <div class="divider">
    <!-- 下拉菜单 -->
    <div class="title-container" v-if="selectData.length > 1">
      <span class="title">{{ title }}</span>
    </div>
    <div v-else class="title">{{ title }}</div>

    <div class="divider-line"></div>

    <slot> </slot>
    <div class="update-btn">
      <el-select
        v-model="currentPackage"
        placeholder="Select"
        style="width: 70px"
        size="small"
        v-if="selectData.length > 1 && !isSaveLocal"
        class="mr-3 default-select"
      >
        <el-option
          v-for="item in selectData"
          :key="item.value"
          :label="item.label"
          :value="item.value"
          @click="handlePackageChange(item)"
        />
      </el-select>
      <el-button :type="'info'" link @click="handleCancel" v-if="isSaveLocal"> 取消 </el-button>
      <el-button
        :type="isSaveLocal ? 'warning' : 'primary'"
        link
        @click="handelUpdateOrSave"
        :disabled="isDisabled"
      >
        {{ isSaveLocal ? '保存' : '编辑' }}
      </el-button>
    </div>
  </div>
</template>

<style scoped lang="scss">
.divider {
  display: flex;
  align-items: center;
  gap: 8px;
  .title {
    font-size: 16px;
    font-weight: bold;
    color: black;
  }
  .title-container {
    display: flex;
    align-items: center;

    .el-dropdown-link {
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    .package-name {
      margin-left: 4px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px;
    }

    .el-icon--right {
      margin-left: 4px;
    }
  }

  .divider-line {
    border-top: 1px solid var(--el-border-color);
    flex: 1;
    margin: 0 12px;
  }
}
/* 通过更高优先级选择器恢复默认 */
:deep(.default-select.el-select--small .el-select__wrapper) {
  gap: 4px;
  padding: 2px 8px;
  min-height: 24px;
  line-height: 20px;
  font-size: 12px;
}
:deep(.active-item) {
  background-color: var(--el-color-primary-light-9) !important; // 你可以自定义颜色
  color: var(--el-color-primary);
  font-weight: bold;
}
</style>
