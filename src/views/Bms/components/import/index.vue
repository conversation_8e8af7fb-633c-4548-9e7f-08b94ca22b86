<script setup lang="ts">
defineOptions({ name: 'ImportView' })
import { ElLoading } from 'element-plus'
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled } from '@element-plus/icons-vue'
import { getAccessToken } from '@/utils/auth'

const props = defineProps({
  uploadUrl: {
    type: String,
    default: ''
  }
})

// emits
const emit = defineEmits(['success'])

// 控制状态
const dialogVisible = ref(false)
const loading = ref(false)
const uploadProgress = ref(0)

// 上传配置项
const acceptTypes = ref(['.xlsx', '.xls']) // 允许的文件类型
const maxSize = ref(50) // 最大文件大小 (MB)
const allowMultiple = ref(false) // 是否支持多文件上传

const uploadUrl = computed(() => `${import.meta.env.VITE_API_URL}${props?.uploadUrl}`)

// 上传前校验
const beforeUpload = (file: File) => {
  const ext = file.name.substring(file.name.lastIndexOf('.'))
  if (!acceptTypes.value.includes(ext)) {
    ElMessage.error(`仅支持 ${acceptTypes.value.join(', ')} 格式文件`)
    return false
  }

  const isLtMaxSize = file.size / 1024 / 1024 < maxSize.value
  if (!isLtMaxSize) {
    ElMessage.error(`文件大小不能超过 ${maxSize.value}MB!`)
    return false
  }

  return true
}

// 开始上传
const handelChange = (val) => {
  const loading = ElLoading.service({
    lock: true,
    text: '文件导入中...',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  if (val?.status == 'success') loading.close()
}

// 上传成功回调
const handleSuccess = (res: { code: number; msg?: string }) => {
  if (res?.code !== 0) return ElMessage.error(res?.msg || '上传失败')

  ElMessage.success('上传成功')
  emit('success')
  dialogVisible.value = false
}

// 上传失败回调
const handleError = () => {
  ElMessage.error('上传失败，请重试')
}

// 对外暴露 open 方法
const open = () => {
  dialogVisible.value = true
}
defineExpose({ open })
</script>

<template>
  <el-dialog v-model="dialogVisible" title="导入" width="32%" :draggable="true">
    <el-upload
      drag
      class="p-3"
      :action="uploadUrl"
      :headers="{ Authorization: `Bearer ${getAccessToken()}` }"
      :before-upload="beforeUpload"
      :on-success="handleSuccess"
      :on-error="handleError"
      :on-change="handelChange"
      :show-file-list="false"
      :auto-upload="true"
      :multiple="allowMultiple"
    >
      <el-icon class="el-icon--upload"><upload-filled /></el-icon>
      <div class="el-upload__text"> 将文件拖到此处，或<em>点击上传</em> </div>
      <div class="el-upload__tip">
        支持 {{ acceptTypes.join(', ') }} 格式，且不超过 {{ maxSize }}MB
      </div>
    </el-upload>

    <el-progress v-if="loading" :percentage="uploadProgress" :stroke-width="2" status="success" />
  </el-dialog>
</template>

<style scoped lang="scss">
.el-upload {
  width: 100%;
}
.el-upload-dragger {
  width: 100%;
  padding: 40px 0;
}
</style>
