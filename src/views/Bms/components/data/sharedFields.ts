// 共享字段定义
// 批次信息字段batchFields.ts
export const batchFields = {
  name: {
    field: 'name',
    label: '批次名称:'
  },
  uploadDifficulty: {
    field: 'uploadDifficulty',
    label: '上传难度:'
  },
  region: {
    field: 'region',
    label: '地区:'
  },
  property: {
    field: 'property',
    label: '属性:'
  },
  platform: {
    field: 'platform',
    label: '平台:'
  },
  purchaser: {
    field: 'purchaser',
    label: '采购人:'
  },
  bidOpeningDate: {
    field: 'bidOpeningDate',
    label: '开标日期:'
  },
  bidPurchaseDate: {
    field: 'bidPurchaseDate',
    label: '买标截止日期:'
  },
  procurementNumber: {
    field: 'procurementNumber',
    label: '采购编号:'
  },
  institutionName: {
    field: 'institutionName',
    label: '代理机构:'
  },
  agencyFee: {
    field: 'agencyFee',
    label: '招标代理费（万元）:'
  },
  isPay: {
    field: 'isPay',
    label: '是否已交:'
  },
  institutionTel: {
    field: 'institutionTel',
    label: '代理机构联系电话:'
  }
} as const

// 包信息字段packageFields.ts
export const packageFields = {
  bagName: {
    field: 'bagName',
    label: '包名称:'
  },
  bagNum: {
    field: 'bagNum',
    label: '包号:'
  },
  subBidName: {
    field: 'subBidName',
    label: '分标名称:'
  },
  subType: {
    field: 'subType',
    label: '投标类型:'
  },
  margin: {
    field: 'margin',
    label: '保证金（万元）:'
  },
  serviceType: {
    field: 'serviceType',
    label: '服务类型:'
  },
  subDomain: {
    field: 'subDomain',
    label: '参与公司:'
  },
  subBidNumber: {
    field: 'subBidNumber',
    label: '分标编号:'
  },
  // 是否公告
  //   isNotice: {
  //     field: 'isNotice',
  //     label: '是否公告',
  //   },
  uploadStatus: {
    field: 'uploadStatus',
    label: '文件上传情况:'
  },
  weight: {
    field: 'weight',
    label: '商务/技术/报价评分权重(%):'
  },
  isBid: {
    field: 'isBid',
    label: '是否中标:'
  },
  quotationMethod: {
    field: 'quotationMethod',
    label: '报价方式:'
  },
  winningBidder: {
    field: 'winningBidder',
    label: '中标单位:'
  },
  noticeAward: {
    field: 'noticeAward',
    label: '中标通知书:'
  },
  businessDirector: {
    field: 'businessDirector',
    label: '商务负责人:'
  },
  technicalDirector: {
    field: 'technicalDirector',
    label: '技术负责人:'
  },
  uploadDirector: {
    field: 'uploadDirector',
    label: '上传负责人:'
  }
} as const

// 项目信息字段 projectFields.ts
export const projectFields = {
  projectName: {
    field: 'projectName',
    label: '项目名称:'
  },
  maximumLimit: {
    field: 'maximumLimit',
    label: '最高限额:'
  },
  budgetAmount: {
    field: 'budgetAmount',
    label: '预算金额:'
  },
  initiationNumber: {
    field: 'initiationNumber',
    label: '立项编号:'
  },
  projectOverview: {
    field: 'projectOverview',
    label: '项目概况/采购内容/采购范围:'
  },
  projectUnit: {
    field: 'projectUnit',
    label: '项目单位/需求单位:'
  },
  requirements: {
    field: 'requirements',
    label: '专用资格要求:'
  },
  performance: {
    field: 'performance',
    label: '业绩标准:'
  },
  personnel: {
    field: 'personnel',
    label: '人员要求:'
  }
} as const
