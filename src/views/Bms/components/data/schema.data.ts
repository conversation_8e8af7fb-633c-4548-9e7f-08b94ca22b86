import { FormSchema } from '@/types/form'
import { DescriptionsSchema } from '@/types/descriptions'
import { batchFields, packageFields, projectFields } from './sharedFields'
import { DICT_TYPE, getIntDictOptions } from '@/utils/dict'
import { useValidator } from '@/hooks/web/useValidator'
const col = 12
const width = '90%'
const { required } = useValidator()
// 基础批次信息表单配置
export const batchInfoSchema: FormSchema[] = [
  {
    ...batchFields.name,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      clearable: true,
      style: { width }
    },
    formItemProps: { rules: [required()] }
  },

  {
    ...batchFields.platform,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width }
    },
    formItemProps: { rules: [required()] }
  },
  {
    ...batchFields.region,
    // component: 'Cascader',
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width }
      // placeholder: '请选择地区'
    },
    formItemProps: { rules: [required()] }
  },
  {
    ...batchFields.uploadDifficulty,
    component: 'Select',
    colProps: { span: col },
    componentProps: {
      clearable: true,
      style: { width },
      options: getIntDictOptions(DICT_TYPE.BMS_UPLOAD_DIFFICULTY)
    }
    // formItemProps: { rules: [required()] }
  },
  {
    ...batchFields.property,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width }
    }
  },

  {
    ...batchFields.purchaser,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      filterable: true,
      style: { width: width }
      // options: [] // 动态注入采购人选项
    }
    // formItemProps: { rules: [required()] }
  },
  // 开标日期
  {
    ...batchFields.bidOpeningDate,
    component: 'DatePicker',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      placeholder: '请选择日期',
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    },
    formItemProps: {
      // rules: [required()]
    }
  },
  {
    ...batchFields.bidPurchaseDate,
    component: 'DatePicker',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      placeholder: '请选择日期',
      type: 'date',
      valueFormat: 'YYYY-MM-DD'
    },
    formItemProps: {
      // rules: [required()]
    }
  },
  {
    ...batchFields.procurementNumber,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width }
    }
  },

  {
    ...batchFields.institutionName,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      // multiple: true,
      filterable: true,
      style: { width: width }
      // options: []
    }
  },
  // 招标代理费
  {
    ...batchFields.agencyFee,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      clearable: true
    }
  },
  //是否已交
  {
    ...batchFields.isPay,
    component: 'Select',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      options: getIntDictOptions(DICT_TYPE.BMS_IS_PAY)
    }
  },
  {
    ...batchFields.institutionTel,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      filterable: true,
      style: { width: width }
    }
  }
]

export const batchInfoDescriptionsSchema: DescriptionsSchema[] = [
  {
    ...batchFields.name,
    // span: 24,
    labelAlign: 'right'
  },
  {
    ...batchFields.platform
  },
  {
    ...batchFields.region,
    labelAlign: 'right'
  },
  {
    ...batchFields.uploadDifficulty,
    dictType: DICT_TYPE.BMS_UPLOAD_DIFFICULTY
  },
  {
    ...batchFields.property,
    labelAlign: 'right'
  },

  {
    ...batchFields.purchaser
  },
  {
    ...batchFields.bidOpeningDate
  },
  {
    ...batchFields.bidPurchaseDate
  },
  {
    ...batchFields.procurementNumber
  },
  {
    ...batchFields.institutionName
  },
  {
    ...batchFields.agencyFee
  },
  {
    ...batchFields.isPay,
    dictType: DICT_TYPE.BMS_IS_PAY
  },
  {
    ...batchFields.institutionTel
  }
]

//包号、包名称、采购人、报价方式等基本信息表单配置
export const packageBasicInformation = (): FormSchema[] => {
  return [
    {
      ...packageFields.bagName,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        clearable: true,
        style: { width }
      },
      formItemProps: { rules: [required()] }
    },
    {
      ...packageFields.bagNum,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        clearable: true,
        style: { width: width }
      },
      formItemProps: { rules: [required()] }
    },

    {
      ...packageFields.subBidName,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        style: { width: width }
      }
    },
    {
      ...packageFields.subType,
      component: 'Select',
      colProps: { span: col },
      componentProps: {
        style: { width: width },
        options: getIntDictOptions(DICT_TYPE.BMS_SUB_TYPE)
      }
      // formItemProps: { rules: [required()] }
    },
    // 保证金（万）
    {
      ...packageFields.margin,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        clearable: true,
        style: { width: width }
      }
      // formItemProps: { rules: [required()] }
    },
    {
      ...packageFields.subBidNumber,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        style: { width: width }
      }
    },
    {
      ...packageFields.uploadStatus,
      component: 'Select',
      colProps: { span: col },
      componentProps: {
        style: { width: width },
        options: getIntDictOptions(DICT_TYPE.BMS_UPLOAD_STATUS)
      }
      // formItemProps: { rules: [required()] }
    },
    {
      ...packageFields.weight,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        filterable: true,
        style: { width: width }
      }
    },
    // 是否公告
    // {
    //   label: '是否公告',
    //   field: 'isNotice',
    //   component: 'Select',
    //   colProps: { span: col },
    //   componentProps: {
    //     style: { width: width },
    //     options: [
    //       { value: '1', label: '是' },
    //       { value: '2', label: '否' }
    //     ]
    //   }
    // },
    // 是否中标
    {
      ...packageFields.isBid,
      component: 'Select',
      colProps: { span: col },
      componentProps: {
        style: { width: width },
        options: getIntDictOptions(DICT_TYPE.BMS_IS_BID)
      }
    },

    // 中标单位
    {
      ...packageFields.winningBidder,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        style: { width: width }
        // options: []
      }
    },
    // 中标通知书
    {
      ...packageFields.noticeAward,
      component: 'Select',
      colProps: { span: col },
      componentProps: {
        style: { width: width },
        options: getIntDictOptions(DICT_TYPE.WHETHER_TO_ACCEPT_WINNING_BID)
      }
    },
    {
      ...packageFields.businessDirector,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        filterable: true,
        style: { width: width }
      }
    },

    {
      ...packageFields.technicalDirector,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        filterable: true,
        style: { width: width }
      }
    },

    {
      ...packageFields.uploadDirector,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        filterable: true,
        style: { width: width }
      }
    },

    {
      ...projectFields.requirements,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        type: 'textarea',
        rows: 1,
        style: { width: width },
        clearable: true
      }
    },
    {
      ...projectFields.performance,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        type: 'textarea',
        rows: 1,
        style: { width: width },
        clearable: true
      }
    },
    {
      ...projectFields.personnel,
      component: 'Input',
      colProps: { span: col },
      componentProps: {
        type: 'textarea',
        rows: 1,
        style: { width: width },
        clearable: true
      }
    }
  ]
}

export const formSchemaTip = (treeDataList?: [], domainData?: []): FormSchema[] => {
  return [
    //服务类目、公司主体、报价类型单独抽出来特殊处理
    {
      ...packageFields.serviceType,
      component: 'Cascader',
      labelMessage: '该字段修订将直接影响当前投标报价和具体明细，请谨慎修改！！',
      colProps: { span: col },
      componentProps: {
        props: {
          emitPath: false // 关键配置：只返回最后一级的 value
        },
        style: { width: width },
        options: treeDataList
      },
      formItemProps: {
        rules: [required()]
      }
    },
    {
      ...packageFields.subDomain,
      component: 'Select',
      labelMessage: '该字段修订将直接影响当前投标报价和具体明细，请谨慎修改！！',
      colProps: { span: col },
      componentProps: {
        multiple: true, // 启用多选模式
        // collapseTags: true, // 多选时折叠标签
        // collapseTagsTooltip: true, // 鼠标悬停显示全部选中项
        style: { width: width },
        options: domainData
      },

      formItemProps: { rules: [required()] }
    },

    {
      ...packageFields.quotationMethod,
      component: 'Select',
      colProps: { span: col },
      labelMessage: '该字段修订将直接影响当前投标报价和具体明细，请谨慎修改！！',
      componentProps: {
        filterable: true,
        style: { width: width },
        options: getIntDictOptions(DICT_TYPE.BMS_QUOTATION_METHOD)
      },
      formItemProps: {
        rules: [required()],
        style: {
          color: 'red'
        }
      }
    }
  ]
}

export const packageDescriptionsSchema: DescriptionsSchema[] = [
  {
    ...packageFields.bagName
  },
  {
    ...packageFields.bagNum
  },
  {
    ...packageFields.subBidName
  },
  {
    ...packageFields.subType,
    dictType: DICT_TYPE.BMS_SUB_TYPE
  },
  {
    ...packageFields.margin
  },
  {
    ...packageFields.subBidNumber
  },
  {
    ...packageFields.uploadStatus,
    dictType: DICT_TYPE.BMS_UPLOAD_STATUS
  },
  {
    ...packageFields.weight
  },
  {
    ...packageFields.isBid,
    dictType: DICT_TYPE.BMS_IS_BID
  },

  {
    ...packageFields.winningBidder
  },
  {
    ...packageFields.noticeAward,
    dictType: DICT_TYPE.WHETHER_TO_ACCEPT_WINNING_BID
  },
  {
    ...packageFields.businessDirector
  },
  {
    ...packageFields.technicalDirector
  },
  {
    ...packageFields.uploadDirector
  },
  {
    field: 'serviceTypePath', // 使用处理后的路径字段
    label: '服务类型:'
  },
  {
    field: 'companyName',
    label: '参与公司:'
  },
  {
    ...packageFields.quotationMethod,
    dictType: DICT_TYPE.BMS_QUOTATION_METHOD
  },
  {
    ...projectFields.requirements
  },
  {
    ...projectFields.performance
  },
  {
    ...projectFields.personnel
  }
]

// 项目立项信息表单配置
export const projectItemSchema: FormSchema[] = [
  {
    ...projectFields.projectName,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      placeholder: '请输入项目名称',
      style: { width },
      clearable: true
    },
    formItemProps: { rules: [required()] }
  },

  {
    ...projectFields.maximumLimit,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      placeholder: '请输入金额',
      clearable: true,
      style: { width: width },
      append: '万元' // 支持 Element UI 的 append slot
    },
    formItemProps: { rules: [required()] }
  },
  {
    ...projectFields.budgetAmount,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      placeholder: '请输入金额',
      clearable: true,
      style: { width: width },
      append: '万元' // 支持 Element UI 的 append slot
    }
    // formItemProps: { rules: [required()] }
  },
  // 立项编号
  {
    ...projectFields.initiationNumber,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      clearable: true
    }
  },
  {
    ...projectFields.projectOverview,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      clearable: true
    }
  },
  {
    ...projectFields.projectUnit,
    component: 'Input',
    colProps: { span: col },
    componentProps: {
      style: { width: width },
      clearable: true
    }
  }
]

export const projectDescriptionsSchema: DescriptionsSchema[] = [
  {
    ...projectFields.projectName
  },
  {
    ...projectFields.maximumLimit
  },
  {
    ...projectFields.budgetAmount
  },
  {
    ...projectFields.initiationNumber
  },
  {
    ...projectFields.projectOverview
  },
  {
    ...projectFields.projectUnit
  }
]
