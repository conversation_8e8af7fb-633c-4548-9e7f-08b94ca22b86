<template>
  <div>
    <divider
      v-model:isSave="isSave"
      :selectData="selectOptions"
      :title="title"
      @package-change="handlePackageChange"
      @handel-save="handelSave"
      :component-id="'project'"
      @close="() => (isSave = false)"
    />
    <div class="info-panel">
      <!-- 展示模式 -->
      <Descriptions
        v-if="!isSave"
        :column="2"
        :label-align="'right'"
        :border="false"
        :collapse="false"
        :data="processedData"
        :schema="descSchema"
        empty-text="-"
        class="right-label-descriptions"
      />

      <!-- 编辑模式 -->
      <Form
        v-else
        class="mt-4"
        size="small"
        label-position="right"
        :schema="formSchema"
        @register="registerInfo"
        :is-col="true"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useForm } from '@/hooks/web/useForm'
import divider from '@/views/Bms/components/divider/index.vue'
import { DescriptionsSchema } from '@/types/descriptions'
import { FormSchema } from '@/types/form'
import { useTenderStoreWithOut } from '@/store/modules/bms/tender'
import * as _ from 'lodash-es'

const useTenderStore = useTenderStoreWithOut()

interface Props {
  title: string
  data: Record<string, any>[]
  selectOptions?: Record<string, any>[]
  descSchema: DescriptionsSchema[]
  formSchema: FormSchema[]
}

const props = defineProps<Props>()

const quotationMethodMap: Record<number, 'total' | 'discount' | 'price'> = {
  1: 'discount',
  2: 'total',
  3: 'price'
}
const emit = defineEmits(['projectInfoSubmit', 'projectChange'])

interface FormData {
  bidOpeningDate?: Date | string | null // 可能是 Date、string 或 null
  [key: string]: any // 其他动态字段
}

const { methods: basicInfo, elFormRef: elFormInfoRef, register: registerInfo } = useForm()
const { getFormData: getInfoFormData, setValues: setInfoValues } = basicInfo

const isSave = ref(false)

const processedData = computed(() => {
  if (!props.data) return {}
  const cloneData: any = _.cloneDeep(props.data)
  const method = quotationMethodMap[useTenderStore.getQuotationMethod]
  const suffix = method === 'discount' ? '%' : '万元'
  cloneData.maximumLimit = `${cloneData.maximumLimit ?? ''}${suffix}`
  return cloneData
})

// 处理项目切换
const handlePackageChange = async (val: string) => {
  emit('projectChange', val)
}

// 保存处理函数
const handelSave = async () => {
  const basicFromData = (await getInfoFormData()) as FormData
  const basicValid = await elFormInfoRef?.value.validate()
  if (!basicValid) return
  emit('projectInfoSubmit', basicFromData)
  isSave.value = false
}

watch(
  () => isSave.value,
  (newVal) => {
    if (newVal) {
      setInfoValues(props.data)
    }
  }
)
</script>

<style scoped lang="scss">
@import url(./style.scss);
</style>
