<template>
  <divider
    v-model:isSave="isSave"
    :selectData="selectOptions"
    :title="title"
    @package-change="handlePackageChange"
    @handel-save="handelSave"
    :component-id="'package'"
    @close="() => (isSave = false)"
  />

  <div class="info-panel">
    <!-- 展示模式 -->
    <Descriptions
      v-if="!isSave"
      :column="2"
      :label-align="'right'"
      :border="false"
      :collapse="false"
      :data="processedData"
      :schema="descSchema"
      empty-text="-"
      class="right-label-descriptions"
    >
<!--      <el-icon><QuestionFilled /></el-icon>-->
      <template #companyName-label="{ row }">
       <span>
       {{ row.label  }}
    <el-tooltip content="默认第一个为主标公司" placement="top">
      <el-icon class="hint-icon"><QuestionFilled /></el-icon>
    </el-tooltip>
  </span>
      </template>
     </Descriptions>

    <!-- 编辑模式 -->
    <Form
      v-else
      class="mt-4"
      size="small"
      label-position="right"
      :schema="formSchema"
      @register="registerInfo"
      :is-col="true"
    />
    <!-- 提示信息 -->
    <el-divider v-if="isSave">
      <div class="form-notice"> 以下字段修订将直接影响当前投标报价和具体明细,请谨慎修改!!</div>
    </el-divider>

    <!-- 服务类型、公司、报价方式字段 -->
    <Form
      v-if="isSave"
      size="small"
      label-position="right"
      :schema="formSchemaTip"
      @register="registerTipInfo"
      :is-col="true"
    />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useForm } from '@/hooks/web/useForm'
import divider from '@/views/Bms/components/divider/index.vue'
import { DescriptionsSchema } from '@/types/descriptions'
import { FormSchema } from '@/types/form'
import { QuestionFilled } from '@element-plus/icons-vue'
interface Props {
  title: string
  data: Record<string, any>[]
  selectOptions?: Record<string, any>[]
  descSchema: DescriptionsSchema[]
  formSchema: FormSchema[]
  formSchemaTip: FormSchema[]
}

const props = defineProps<Props>()

const emit = defineEmits(['bagInfoSubmit', 'bagChange'])

interface FormData {
  bidOpeningDate?: Date | string | null // 可能是 Date、string 或 null
  [key: string]: any // 其他动态字段
}

const { methods: basicInfo, elFormRef: elFormInfoRef, register: registerInfo } = useForm()
const { methods: tipInfo, elFormRef: elFormTipRef, register: registerTipInfo } = useForm()
const { getFormData: getInfoFormData, setValues: setInfoValues } = basicInfo
const { getFormData: getTioFormData, setValues: setTipValues } = tipInfo
const currentBag = ref()

const isSave = ref(false)

const processedData = computed(() => {
  if (!props.data) return {}
  return { ...props.data } // 直接返回原始数据，不转换 null/undefined
})

// 处理包切换
const handlePackageChange = async (val: string) => {
  await nextTick()
  currentBag.value = val
  emit('bagChange', val)
}

// 保存处理函数
const handelSave = async () => {
  const basicFromData = (await getInfoFormData()) as FormData

  const basicFromDataTip = (await getTioFormData()) as FormData
  // 合并 tip 表单的数据到主表单数据中
  for (const key in basicFromDataTip) {
    if (props.formSchemaTip?.some((field) => field.field === key)) {
      basicFromData[key] = basicFromDataTip[key]
    }
  }
  const basicValid = await elFormInfoRef?.value.validate()
  const basicValidTip = await elFormTipRef?.value.validate()
  if (!basicValid && !basicValidTip) return
  emit('bagInfoSubmit', basicFromData)
  isSave.value = false
}

watch(
  () => isSave.value,
  (newVal) => {
    if (newVal) {
      setInfoValues(props.data)
      setTipValues(props.data)
    }
  }
)
</script>

<style scoped lang="scss">
@import url(./style.scss);
.form-notice {
  color: #e64d4d; /* 红色提示 */
  font-size: 12px;
  font-weight: bold;
  white-space: pre-wrap;
  margin-bottom: 6px;
}
:deep(.el-divider--horizontal) {
  width: 99%;
}

.btn {
  position: relative;
  top: 10px;
  right: 10px;
}
</style>
