<template>
  <div>
    <divider
      v-model:isSave="isSave"
      :selectData="selectOptions"
      :title="title"
      @handel-save="handelSave"
      @close="() => (isSave = false)"
      :component-id="'batch'"
    />
    <div class="info-panel">
      <!-- 展示模式 -->
      <Descriptions
        v-if="!isSave"
        :column="2"
        :label-align="'center'"
        :border="false"
        :data="processedData"
        :schema="descSchema"
        empty-text="--"
        class="right-label-descriptions"
      />

      <!-- 编辑模式 -->
      <Form
        v-else
        class="mt-4"
        size="small"
        label-position="right"
        :schema="formSchema"
        @register="registerInfo"
        :is-col="true"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useForm } from '@/hooks/web/useForm'
import divider from '@/views/Bms/components/divider/index.vue'
import { DescriptionsSchema } from '@/types/descriptions'
import { FormSchema } from '@/types/form'
import { useDateFormatter } from '@/views/Bms/hooks/useDateFormatter'
interface Props {
  title: string
  data: Record<string, any>[]
  selectOptions?: Record<string, any>[]
  descSchema: DescriptionsSchema[]
  formSchema: FormSchema[]
}
const { safeFormatDateToISO } = useDateFormatter()

const props = defineProps<Props>()

const emit = defineEmits(['batchInfoSubmit'])

interface FormData {
  bidOpeningDate?: Date | string | null // 可能是 Date、string 或 null
  [key: string]: any // 其他动态字段
}

const { methods: basicInfo, elFormRef: elFormInfoRef, register: registerInfo } = useForm()
const { getFormData: getInfoFormData, setValues: setInfoValues } = basicInfo

const isSave = ref(false)

const processedData = computed(() => {
  if (!props.data) return {}
  return { ...props.data } // 直接返回原始数据，不转换 null/undefined
})
// 保存处理函数
const handelSave = async () => {
  const basicFromData = (await getInfoFormData()) as FormData

  const timeFields = ['bidPurchaseDate', 'bidOpeningDate']
  // 格式化时间字段
  timeFields.forEach((field) => {
    if (basicFromData[field] instanceof Date) {
      basicFromData[field] = safeFormatDateToISO(basicFromData[field])
    }
  })
  const basicValid = await elFormInfoRef?.value.validate()
  if (!basicValid) return
  emit('batchInfoSubmit', basicFromData)
  isSave.value = false
}

watch(
  () => isSave.value,
  (newVal) => {
    if (newVal) {
      setInfoValues(props.data)
    }
  }
)
</script>

<style scoped lang="scss">
@import url(./style.scss);
:deep(.el-icon svg) {
  height: 1.5em;
  width: 1.5em;
}
</style>
