<template>
  <ContentWrap>
    <div class="doc-container">
      <div class="doc-certer">
        <div class="header">
          <span>文档格式仅限于：.pdf格式文档，上传后文档系统不会保留</span>
        </div>

        <div class="file-list-container">
          <el-table
            :data="fileList"
            v-loading="loading"
            style="width: 100%"
            max-height="500px"
            :header-cell-style="{ background: '#f5f7fa', fontWeight: 'bold' }"
          >
            <!-- 文件名列 -->
            <el-table-column prop="fileName" label="文件名称" min-width="200" align="center">
              <template #default="{ row }">
                <span>{{ row.fileName }}</span>
              </template>
            </el-table-column>

            <!-- 状态列 -->
            <el-table-column prop="status" label="状态" width="180"  align="center">
              <template #default="{ row }">
                <div class="status-cell">
                  <el-tag :type="statusTagType(row.status)" :effect="row.status === 'processing' ? 'light' : 'plain'">
                    <template v-if="row.status === 'success'">
                      <el-icon><CircleCheck /></el-icon>
                      <span>已完成</span>
                    </template>
                    <template v-else-if="row.status === 'processing'">
                      <el-icon class="is-loading"><Loading /></el-icon>
                      <span>处理中</span>
                    </template>
                    <template v-else>
                      <el-icon><CircleClose /></el-icon>
                      <span>处理失败</span>
                    </template>
                  </el-tag>
                  <div v-if="row.message" class="sse-message" :title="row.message">
                    {{ row.message }}
                  </div>
                </div>
              </template>
            </el-table-column>

            <!-- 文件大小列 -->
<!--            <el-table-column prop="processingTime" label="处理时间" width="150" align="center">-->
<!--              <template #default="{ row }">-->
<!--                      <span v-if="row.status === 'processing'">-->
<!--                        <el-icon class="is-loading"><Loading /></el-icon>-->
<!--                        计算中...-->
<!--                      </span>-->
<!--                   <span v-else>-->
<!--                  {{ formatProcessingTime(row.processingTime) }}-->
<!--                </span>-->
<!--              </template>-->
<!--            </el-table-column>-->

            <!-- 操作列 -->
            <el-table-column label="操作" width="150"  align="center">
              <template #default="{ row }">
                <el-button
                  size="small"
                  type="warning"
                  plain
                  class="el-button--warning is-link"
                  :disabled="row.status !== 'success'"
                  @click="handleDownload(row)"
                >
                  <el-icon><Download /></el-icon>
                  下载
                </el-button>
              </template>
            </el-table-column>

            <!-- 空状态 -->
            <template #empty>
              <div class="empty-state">
                <el-empty description="暂无文件" />
              </div>
            </template>
          </el-table>
        </div>
        <div class="bottom-view">
          <el-upload
            class="upload-btn"
            :show-file-list="false"
            :auto-upload="true"
            :action="uploadUrl"
            :headers="{ Authorization: `Bearer ${getAccessToken()}` }"
            :before-upload="beforeUpload"
            :on-change="handelChange"
            :data="{ language: 'chi_sim+eng', outputType: 'pdfa', forceOcr: 'on' }"
            :multiple="false"
            accept=".pdf"
          >
            <el-button type="danger">
              <el-icon><Upload /></el-icon>
              上传文件
            </el-button>
          </el-upload>
        </div>
      </div>
    </div>
  </ContentWrap>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import * as Api from '@/api/Bms/pdfTiger/index'
import { Upload, CircleCheck, CircleClose, View, Download, Loading } from '@element-plus/icons-vue'
import { ocrSSE  } from './sseHelper'
import type { UploadRequestOptions } from 'element-plus'
import { ElMessage } from 'element-plus'
import { getAccessToken } from '@/utils/auth'
import download from '@/utils/download'
import { downloadPdfOcr } from '@/api/Bms/pdfTiger/index'

interface DocFile {
  id: string
  fileName: string
  taskId: string
  type: 'pdf'
  status: 'processing' | 'success' | 'error'
  size: number
  url?: string
  startTime?: number // 记录开始处理的时间戳
  endTime?: number // 记录完成处理的时间戳
  processingTime?: number // 计算得出的处理时间(毫秒)
  sseConnection?: EventSource
}
const uploadUrl = computed(() => `${import.meta.env.VITE_API_URL}/bms/double/layer`)

const loading = ref(false)
const fileList = ref<DocFile[]>([])

// 初始化加载文件列表
onMounted(() => {

  fetchFileList()
})
onUnmounted(() => {
  ocrSSE.disconnect()
  ocrSSE.off('message') // 移除监听
})
// 状态标签类型映射
// 状态标签类型映射
const statusTagType = (status: string) => {
  const typeMap: Record<string, 'success' | 'warning' | 'danger' | 'info'> = {
    'success': 'success',
    'processing': 'warning',
    'error': 'danger'
  }
  return typeMap[status] || 'info'
}



// 监听SSE消息
const setupSSEListener = () => {
  ocrSSE.on('message', (data) => {
    console.log('收到SSE消息:', data)

    // 只处理STATUS类型的消息
    if (data.msgType === 'STATUS') {
      const targetFile = fileList.value.find(f => f.taskId === data.taskId)
      if (targetFile) {
        // 如果是开始处理的消息，记录taskId
        if (!targetFile.taskId) {
          targetFile.taskId = data.taskId
        }

        // 如果是完成状态
        if (data.status === 'COMPLETED') {
          targetFile.endTime = Date.now()
          targetFile.processingTime = targetFile.endTime - (targetFile.startTime || targetFile.endTime)
          targetFile.status = 'success'
          targetFile.url = data.outputUrl || ''
        } else if (data.status === 'PROCESSING') {
          targetFile.endTime = Date.now()
          targetFile.processingTime = targetFile.endTime - (targetFile.startTime || targetFile.endTime)
          targetFile.status = 'error'
        }

        // targetFile.message = data.message || ''
      }
    }
  })

  ocrSSE.on('error', (err) => {
    console.error('SSE错误:', err)
  })

  ocrSSE.on('connected', () => {
    console.log('SSE已连接')
  })
}
// 启动SSE连接
const startSSEConnection = (taskId?: string) => {
  ocrSSE.connect(taskId)
}
// 获取文件列表
const fetchFileList = async () => {
  try {
    loading.value = true
    const response = await Api.getOcrTasksList()
    console.log('API响应数据:', response)
    // 解析响应数据
    // const rawData = await response.json()
    console.log('API响应数据:', response)

    // 转换数据结构（适配后端格式）
    fileList.value = Object.values(response).map((task: any) => ({
      id: task.taskId, // 使用taskId作为唯一标识
      taskId: task.taskId,
      fileName: task.originalFileName,
      type: 'pdf',
      status: convertStatus(task.status), // 状态转换
      size: 0, // 需要后端补充
      url: task.outputFilePath || null,
      log: task.log // 保留日志信息
    }))
  } catch (error) {
    console.error('获取文件列表失败:', error)
    ElMessage.error(`获取列表失败: ${error}`)
  } finally {
    loading.value = false
  }
}

// 状态转换函数
const convertStatus = (serverStatus: string): DocFile['status'] => {
  switch (serverStatus) {
    case 'COMPLETED':
      return 'success'
    case 'PROCESSING':
      return 'processing'
    default:
      return 'error' // 包括FAILED等其他状态
  }
}

// 文件类型校验
const beforeUpload = (file: File) => {
  const validTypes = ['application/pdf']
  const isAllowedType = validTypes.includes(file.type)
  const isLt100M = file.size / 1024 / 1024 < 100

  if (!isAllowedType) {
    ElMessage.error('仅支持上传PDF文档!')
    return false
  }
  if (!isLt100M) {
    ElMessage.error('文件大小不能超过100MB!')
    return false
  }
  setupSSEListener()
  startSSEConnection() // 全局连接
  return true
}

// 处理上传
const handelChange = async (options: UploadRequestOptions) => {
  //上传列表追加数据
  await fetchFileList()

}

// 预览文档
const handlePreview = (file: DocFile) => {
  if (!file.url) return
  window.open(file.url, '_blank')
}

// 下载文件
const handleDownload = async (file: DocFile) => {
  if (!file.taskId) {
    ElMessage.warning('文件任务ID不存在')
    return
  }
     const response = await Api.downloadPdfOcr(file.taskId)
    download.common(response)
}

// 格式化文件大小
const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
// 格式化处理时间为易读格式
const formatProcessingTime = (ms?: number) => {
  if (!ms || ms <= 0) return '-'

  // 转换为秒
  const seconds = Math.floor(ms / 1000)

  if (seconds < 1) return `${ms}毫秒`
  if (seconds < 60) return `${seconds}秒`

  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60

  if (minutes < 60) return `${minutes}分${remainingSeconds}秒`

  const hours = Math.floor(minutes / 60)
  const remainingMinutes = minutes % 60

  return `${hours}小时${remainingMinutes}分${remainingSeconds}秒`
}
</script>

<style scoped>
/* 基础样式保持不变 */
.status-processing .status-icon {
  color: var(--el-color-primary);
}



/* 旋转动画 */
.el-icon.is-loading {
  animation: rotating 2s linear infinite;
}

@keyframes rotating {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
.doc-certer {
  width: 80%;
  margin: 0 auto;
}
.header {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 20px;
  span {
    font-size: 20px;
    font-weight: bold;
  }
}
.bottom-view {
  display: flex;
  justify-content: end;
  align-items: center;
  margin-top: 20px;
}
/* 文件列表样式 */
.file-list-container {
  border: 1px dashed #dcdfe6;
  border-radius: 4px;

  min-height: 400px;
  position: relative;
}

.file-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

/* 表头样式 */
.file-list-header {
  display: flex;
  padding: 12px 16px;
  justify-content: center;
  background-color: #f5f7fa;
  font-weight: bold;
  border-bottom: 1px solid #ebeef5;
  /* 设置各列宽度比例 */
  .file-name {
    flex: 3; /* 文件名列更宽 */
    min-width: 300px; /* 最小宽度保证 */
  }
}

.file-list-header > span {
  flex: 1;
  text-align: center;
}

/* 文件项样式 */
.file-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  transition: background-color 0.3s;
  /* 保持与表头相同的宽度比例 */
  .file-name {
    flex: 3;
    min-width: 300px;
    justify-content: flex-start; /* 文件名左对齐 */
    text-align: center;
  }
}

.file-item:hover {
  background-color: #f5f7fa;
}

.file-item > span {
  flex: 1;
  text-align: center;
}

/* 状态图标 */
.status-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-success .status-icon {
  color: var(--el-color-success);
}

.status-error .status-icon {
  color: var(--el-color-danger);
}

/* 操作按钮 */
.file-actions {
  display: flex;
  justify-content: center;
}
.el-button--warning {
  margin-left: 0;
}
/* 空状态 */
.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 360px;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .file-list-header {
    display: none;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .file-item > span {
    flex: none;
    width: 100%;
    text-align: left;
  }

  .file-actions {
    justify-content: flex-start;
  }
}
</style>
