// src/utils/sse.ts
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getAccessToken } from '@/utils/auth'
import { EventEmitter } from 'eventemitter3'

const baseUrl = import.meta.env.VITE_API_URL || ''

interface SSEMessage {
  event?: string
  data: string
  id?: string
  retry?: number
}

class OcrSSEService extends EventEmitter {
  private controller: AbortController | null = null
  private retryCount = 0
  private maxRetries = 3

  constructor() {
    super()
  }

  public connect(taskId?: string) {
    this.disconnect() // 先断开现有连接

    const url = `${baseUrl}/bms/double/layer/sse`

    this.controller = new AbortController()

    fetchEventSource(url, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${getAccessToken()}`,
        'Content-Type': 'text/event-stream'
      },
      signal: this.controller.signal,
      openWhenHidden: true,

      onopen: async (response) => {
        if (response.ok) {
          this.retryCount = 0
          this.emit('connected')
          console.log('SSE连接成功')
        } else {
          throw new Error(`SSE连接失败: ${response.status}`)
        }
      },

      onmessage: (msg: SSEMessage) => {
        try {
          const parsedData = JSON.parse(msg.data)
          this.emit('message', parsedData)

          // 如果有特定事件类型
          if (msg.event) {
            this.emit(msg.event, parsedData)
          }
        } catch (error) {
          this.emit('raw', msg.data)
        }
      },

      onclose: () => {
        this.emit('disconnected')
        console.log('SSE连接关闭')
      },

      onerror: (err) => {
        console.error('SSE错误:', err)
        this.emit('error', err)

        if (this.retryCount < this.maxRetries) {
          this.retryCount++
          setTimeout(() => this.connect(taskId), 1000 * this.retryCount)
        } else {
          this.emit('failed', err)
        }
      }
    })
  }

  public disconnect() {
    if (this.controller) {
      this.controller.abort()
      this.controller = null
    }
  }
}

// 单例模式导出
export const ocrSSE = new OcrSSEService()
