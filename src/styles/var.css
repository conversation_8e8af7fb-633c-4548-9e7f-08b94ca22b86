:root {
  --login-bg-color: #293146;

  --left-menu-max-width: 200px;

  --left-menu-min-width: 64px;

  --left-menu-bg-color: white !important;

  --left-menu-bg-light-color: white !important;

  --left-menu-bg-active-checked-color:#e9f1fc !important;

  --left-menu-light-color: black !important;

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-bg-active-color: var(--el-color-primary);

  --left-menu-text-color: #000000 !important;

  --left-menu-text-active-color: #4290F9 !important;

  --left-menu-collapse-bg-active-color: var(--el-color-primary);

  --left-menu-collapse-bg-active-close-color: #E9F1FC;
  /* left menu end */

  /* logo start */
  --logo-height:46px;
  --logo-width:72px;

  --logo-title-text-color: #fff;
  /* logo end */

  /* header start */
  --top-header-bg-color:'#fff';

  --top-header-text-color:'#fff';

  --top-header-hover-color: none !important;

  --top-tool-height: var(--logo-height);
  --top-tool-width: var(--logo-width);
  /*--top-tool-bg:var(url(@/assets/images/layout_bg.png));*/

  --top-tool-p-x: 0;

  --tags-view-height: 35px;
  /* header start */

  /* tab menu start */
  --tab-menu-max-width: 80px;

  --tab-menu-min-width: 30px;

  --tab-menu-collapse-height: 36px;
  /* tab menu end */

  --app-content-padding: 20px;

  --app-content-bg-color:linear-gradient(#E6F0FD,#CBE4FF) ;

  --app-footer-height: 50px;

  --transition-time-02: 0.2s;

}

.dark {
  --app-content-bg-color:linear-gradient(#E6F0FD,#CBE4FF) ;
}

html,
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

}
