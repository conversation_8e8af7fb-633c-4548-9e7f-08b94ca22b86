@use './FormCreate/index.scss';
@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';

/* 解决 element table 表头无法选中复制问题 */
.el-table th {
  user-select: text !important;
}
/* 仅修改横向滚动条（不影响垂直滚动条） */
.el-table .el-scrollbar__bar.is-horizontal {
  height: 12px !important;  /* 仅作用于横向滚动条 */
}
/* 修改横向滚动条高度 */
//.el-scrollbar__bar {
//  height: 12px !important;
//}
// .el-scrollbar__bar.is-vertical {
//   width: 12px !important;
// }
/*
* 解决popovers缩放后位置不对的问题 仅用于通知弹窗 && 需要使用:自定义类名为el-popover-scale
*/
.el-popover-scale {
  scale: 0.9 !important;
  transform: translate(0.6%, -6%) !important;
}

.el-table__header {
  tr {
    th {
      //height:40px;
      background: rgb(248, 248, 249) !important;
      color: black;
    }
  }
}
.el-form {
  .el-row {
    .el-col {
      padding: 0 !important;
    }
  }
}
.el-card {
  border-radius: 10px !important;
  border: none !important;
}
.el-collapse {
  border: none !important;
}
.el-drawer__body {
  padding-top: 0 !important;
}
.el-collapse-item__header {
  font-size: 16px !important;
}

.el-drawer__header {
  align-items: center;
  display: flex;
  color: black !important;
  font-size: 17px !important;
  margin: 0 !important;
  padding: 0 20px !important;
}
.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

.el-sub-menu__title:hover {
  background-color: #e9f1fc !important;
}

.el-menu-item:hover {
  background-color: #e9f1fc !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}
