.page-container {
  height: calc(100vh - 93px);
  display: flex;
  flex-direction: column;
}

/* 固定头部区域 */
.fixed-header {
  position: sticky;
  top: 0;
  z-index: 10;
}

/* 固定 tabs 区域 */
.fixed-tabs {
  position: sticky;
  z-index: 9;
  background-color: white;
}

/* 滚动的内容区域 */
.scrollable-content {
  flex-grow: 1;
  overflow: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* 兼容性 */
.scrollable-content::-webkit-scrollbar {
  display: none;
}
/* 使用深度选择器去除阴影 */
:deep(.el-input__wrapper) {
  box-shadow: none !important;

  .el-input__inner {
    cursor: default !important;
  }
}

:deep(.el-textarea__inner) {
  box-shadow: none !important;
  cursor: default !important;
}
