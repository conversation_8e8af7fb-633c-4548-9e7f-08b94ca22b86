
<template>
  <div class="HeaderSetting" @click="handelToHome">
    <img :src="steeing" class="header_setting_img" />
  </div>
</template>

<script setup lang='ts'>
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import steeing from '@/assets/imgs/home.png'
const permissionStore = usePermissionStoreWithOut()
const source_url = computed(()=> permissionStore.getSourceUrl)


function handelToHome(){
  window.open(source_url.value)
}

</script>


<style lang='scss' scoped>
.HeaderSetting {
  .header_setting_img {
    width: 20px;
    height: 20px;
  }
}
</style>
