<script lang="tsx">
import { defineComponent, computed } from 'vue'
import { Message } from '@/layout/components/Message'
import { HeaderSetting } from '@/layout/components/HeaderSetting'
import { UserInfo } from '@/layout/components/UserInfo'

import RouterSearch from '@/components/RouterSearch/index.vue'

import { useAppStore } from '@/store/modules/app'
import { useDesign } from '@/hooks/web/useDesign'

const { getPrefixCls, variables } = useDesign()

const prefixCls = getPrefixCls('tool-header')

const appStore = useAppStore()


// 搜索图片
const search = computed(() => appStore.search)

// 尺寸图标
const size = computed(() => appStore.getSize)

// 消息图标
const message = computed(() => appStore.getMessage)

export default defineComponent({
  name: 'BlankToolHeader',
  setup() {
    return () => (
      <div
        id={`${variables.namespace}-tool-header`}
        class={[
          prefixCls,
          'h-[var(--top-tool-height)] relative px-[var(--top-tool-p-x)] flex items-center justify-between',
          'dark:bg-[var(--el-bg-color)]'
        ]}
      >
        <div class="h-full flex items-center">
          {search.value ? <RouterSearch isModal={false} /> : undefined}
          {/* 应用 */}
          {size.value ? (
            <Message class="custom-hover" color="var(--top-header-text-color)"></Message>
          ) : undefined}
          {/* 设置 */}
          {message.value ? (
            <HeaderSetting
              class="custom-hover"
              color="var(--top-header-text-color)"
            ></HeaderSetting>
          ) : undefined}
          {/* 用户 */}
          <UserInfo></UserInfo>
        </div>
      </div>
    )
  }
})
</script>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-tool-header;

.#{$prefix-cls} {
  transition: left var(--transition-time-02);
}
</style>
