<script lang="ts" setup>
defineOptions({ name: 'Message' })
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'
import notMessage from '@/assets/imgs/not_message.png'
import defaultNotice from '@/assets/imgs/notice_icon.png'
import { useWebSocket } from '@vueuse/core'
import { getAccessToken } from '@/utils/auth'
// import * as Api from '@/api/system/message/index'

dayjs.extend(relativeTime)

interface MessageItem {
  id: number
  content: {
    title: string
    message: string
  }
  time: Date
  unread: boolean
  type?: any
}

const server = ref(import.meta.env.VITE_WS_API_URL + '?token=' + getAccessToken())
const messages = ref<MessageItem[]>([])
const unreadCount = computed(() => messages.value.filter((msg) => msg.unread).length)

const { data, close } = useWebSocket(server.value, {
  autoReconnect: false,
  heartbeat: false
})

messages.value = [
  {
    id: 1,
    content: {
      title: '【员工档案】个人信息更新确认',
      message:
        '张XX，您好！员工：张XX，以下信息已更新：变更项：手机号旧值：176****3581新值：134****3987 操作人：李XX'
    },
    time: new Date('2025-03-28 10:30:00'),
    unread: true,
    type: 'notice'
  },
  {
    id: 2,
    content: {
      title: '【员工档案】入职信息确认通知 ',
      message:
        'Hi，马小腾，管理员向你发来一份邀请，点击按钮完善个人档案，开启专属职场新体验！<立即完善个人档案>'
    },
    time: new Date('2025-03-28 10:30:00'),
    unread: true,
    type: 'institution'
  }
]

const getMessageList = async () => {
  // todo: 获取消息列表
  // messages.value = await Api.getMessageList()
  // console.log(messages.value)
}

const handleMessageClick = async (msg: MessageItem) => {
  // todo: 走一遍接口标记已读
  // await useMessageStore.markAsRead(msg.id)
  msg.unread = false
}

const markAllAsRead = async () => {
  // todo: 走一遍接口标记全部已读
  // await Api.markAllAsRead()
  messages.value.forEach((msg) => (msg.unread = false))
}

watchEffect(async () => {
  if (!data.value) return

  try {
    if (data.value === 'pong') return
    const jsonMessage = JSON.parse(data.value)
    console.log('收到消息：', jsonMessage)
    const type = jsonMessage.type
    if (!type) return
    const messageContent = JSON.parse(jsonMessage.content)
    if (type === 'single') {
      messages.value.unshift(messageContent)
      return
    }
  } catch (error) {
    console.error(error)
  }
})

onMounted(async () => {
  await getMessageList()
})
onBeforeUnmount(() => {
  if (close) return close()
})
</script>

<template>
  <div class="message">
    <ElPopover :width="480" placement="bottom" trigger="click" popper-class="el-popover-scale">
      <template #reference>
        <ElBadge
          :value="unreadCount < 99 ? unreadCount : '99+'"
          :show-zero="false"
          type="danger"
          class="item"
        >
          <img :src="notMessage" class="message_img" />
        </ElBadge>
      </template>

      <div class="message-list">
        <div class="message-actions">
          <div class="message-actions-title">消息通知</div>
          <ElButton
            text
            type="primary"
            size="small"
            @click="markAllAsRead"
            :disabled="unreadCount === 0"
          >
            全部已读
          </ElButton>
        </div>

        <div v-if="messages.length === 0" class="message-empty">
          <el-empty description="暂无新消息" :image-size="80" />
        </div>

        <div v-else class="message-scroll">
          <div
            v-for="msg in messages"
            :key="msg.id"
            class="message-item"
            :class="{ 'unread-item': msg.unread, 'read-item': !msg.unread }"
            @click="handleMessageClick(msg)"
          >
            <div class="message-content">
              <div class="message-header">
                <div class="message-title-wrapper">
                  <img
                    :src="defaultNotice"
                    :class="msg.unread ? 'message-icon-unread' : 'message-icon'"
                  />
                  <span class="message-title">{{ msg.content.title }}</span>
                </div>
                <span class="message-time">{{ dayjs(msg.time).fromNow() }}</span>
              </div>
              <div class="message-preview">{{ msg.content.message }}</div>
            </div>
          </div>
        </div>
      </div>
    </ElPopover>
  </div>
</template>

<style lang="scss" scoped>
// 头部样式
.message-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 8px 6px 8px;
  border-bottom: 1px solid var(--el-border-color-light);
  .message-actions-title {
    font-size: 16px;
    font-weight: 700;
    color: var(--el-text-color-primary);
  }
}

:deep(.el-badge__content) {
  scale: 0.82;
}
.unread-item {
  position: relative;
  padding-left: 10px;

  .message-title {
    font-weight: 600 !important;
  }
}

.read-item {
  opacity: 0.8;

  .message-title,
  .message-preview {
    color: var(--el-text-color-secondary) !important;
  }
}

.message-list {
  display: flex;
  height: 400px;
  flex-direction: column;

  .message-empty {
    display: flex;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  .message-item {
    display: flex;
    align-items: center;
    padding: 8px 6px;
    border-bottom: 1px solid var(--el-border-color-light);
    gap: 12px;
    transition: background-color 0.2s;
    cursor: pointer;

    .message-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 4px;
    }

    .message-title-wrapper {
      display: flex;
      // align-items: center;
      .message-title {
        margin-top: 2px;
      }
    }

    &:hover {
      background-color: var(--el-color-primary-light-9);
    }

    .message-icon {
      width: 20px;
      height: 20px;
      object-fit: cover;
    }
    // 未读消息图标
    .message-icon-unread {
      width: 20px;
      height: 20px;
      object-fit: cover;
      animation: breathing 2s infinite ease-in-out;
    }
    // 呼吸灯动画
    @keyframes breathing {
      0% {
        transform: scale(1);
        opacity: 1;
      }
      50% {
        transform: scale(1.06);
        opacity: 0.8;
      }
      100% {
        transform: scale(1);
        opacity: 1;
      }
    }

    .message-content {
      display: flex;
      flex-direction: column;
      flex: 1;
      min-width: 0;

      .message-title {
        font-weight: 500;
        color: var(--el-text-color-primary);
      }

      .message-time {
        font-size: 12px;
        color: var(--el-text-color-secondary);
      }

      .message-preview {
        font-size: 12px;
        color: var(--el-text-color-regular);
      }
    }
  }
  .message-item:last-child {
    border-bottom: none;
  }
}

.message_img {
  width: 19px;
  height: 22px;
  padding-top: 2px;
}

.message-scroll {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 8px;

  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: var(--el-border-color-light);
    border-radius: 3px;
  }
}
</style>
