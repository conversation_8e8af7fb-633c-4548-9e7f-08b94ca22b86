<script lang="ts" setup>
import {computed} from 'vue'
import {useAppStore} from '@/store/modules/app'
import {useDesign} from '@/hooks/web/useDesign'
import {useUserStoreWithOut} from '@/store/modules/user'
const userStore = useUserStoreWithOut()
defineOptions({ name: 'Logo' })

const { getPrefixCls } = useDesign()

const prefixCls = getPrefixCls('logo')

const appStore = useAppStore()

const title = computed(() => appStore.getTitle)
</script>

<template>
  <div>
    <router-link
      :class="[
        prefixCls,
        `${prefixCls}__Top`,
        'flex !h-[var(--logo-height)] items-center cursor-pointer pl-8px relative decoration-none overflow-hidden'
      ]"
      :to="userStore.getDefaultRouter"
    ><img
      class="h-[calc(var(--logo-height)-20px)] w-[calc(var(--logo-width)-10px)]"
      src="@/assets/imgs/vijo_logo.png"/>
      <div
        :class="[
          'ml-14px text-16px font-700',
          {
            'text-[var(--top-header-text-color)]': true
          }
        ]"
        style="
           {
            widows: none;
            width: 100px;
            color: white;
            font-style: oblique;
          }
        "
      >
        {{ title }}
      </div>
    </router-link>
  </div>
</template>
