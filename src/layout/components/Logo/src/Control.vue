<template>
  <div class="control">
    <div class="honme">
      <img :src="home" />
      <div>首页</div>
    </div>
    <div class="system_setting">
      <img :src="systemSetting" />
      <div>系统管理</div>
    </div>
  </div>
</template>

<script setup lang='ts'>
import systemSetting from '@/assets/imgs/system_setting.png'
import home from '@/assets/imgs/home.png'
</script>



<style lang='scss' scoped>
.control {
  display: flex;
  width: 160px;
  justify-content: space-between;
  color: #fffefe;
  font-size: 14px;
}
.honme,
.system_setting {
  display: flex;
  align-items: center;
  &:hover {
    cursor: pointer;
  }
  img {
    width: 15px;
    height: 15px;
    padding-right: 4px;
  }
}
</style>
