import request from '@/config/axios'
import download from "@/utils/download";


// 删除文件
export const deleteFile = (id: number) => {
  return request.delete({ url: '/cms/supplier/file/delete?id=' + id })
}
// 下载文件
export const downloadFile = (id: number) => {
  return request.download({url: '/cms/supplier/file/download?id=' + id})
}
// 上传文件
export const updateFile = (url:string,data: any) => {
  return request.upload({ url, data })
}
export const handelDownloadCommon = async (id: number) => {
  try {
    const data = await downloadFile(id)
    download.common(data)
    ElMessage.success('文件下载成功')
  } catch (e) {
    ElMessage.warning('文件下载失败')
  }
}

export enum FILED_URL {
  SUPPLIER_FILE_UPLOAD = 'cms/supplier/file/upload',
}
export enum BIZ_TYPE {

  CMS_SUPPLIER_OTHER = 'CMS_SUPPLIER_OTHER',

}
