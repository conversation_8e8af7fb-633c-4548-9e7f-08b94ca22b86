import request from '@/config/axios'
/*
 * 获取任务列表接口
 */
export const getOcrTasksList = () => {
  return request.getOcr({ url: '/bms/double/layer/tasks' })
}
/*
 * 上传接口 提交OCR任务
 */
export const uploadPdf = (formData: any) => {
  return request.upload({ url: '/bms/double/layer', formData })
}


//获取OCR任务状态
export const getStatus = (taskId: string) => {
  return request.get({ url: `/bms/double/layer/status/${taskId}` })
}
//下载OCR处理后的文件
export const downloadPdfOcr = (taskId: string) => {
  return request.downloadPdf({ url: `/bms/double/layer/download/${taskId}` })
}
