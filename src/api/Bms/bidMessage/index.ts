import request from '@/config/axios'

/*
 * 批次分分页列表
 */
export const getBidTableList = (params: any) => {
  return request.get({ url: '/bms/hit/list/page', params })
}

/*
 * 选中接口确认
 */
export const hitRecords = (data: any) => {
  return request.post({ url: '/bms/hit/records', data })
}

// 合同模板下载
export const templateDownload = () => {
  return request.download({ url: '/bms/bid/download/questionnaires' })
}

// 状态的下拉
export const getHitStatusOption = () => {
  return request.get({ url: '/bms/hit/option' })
}
