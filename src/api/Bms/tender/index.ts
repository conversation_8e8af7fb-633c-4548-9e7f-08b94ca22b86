import request from '@/config/axios'

/*
 * 批次分分页列表
 */
export const getBidTableList = (params: any) => {
  return request.get({ url: '/bms/bid/list/page', params })
}

/*
 * 根据项目ID查询报价信息
 */
export const getQuotationDetail = (pid: number) => {
  return request.get({ url: `/bms/quotation/load/${pid}` })
}

/*
 * 报价逻辑更新
 */
export const quotationUpdate = (data: any) => {
  return request.post({ url: '/bms/quotation/update', data })
}
/*
 * 报价逻辑锁定
 */
export const quotationLock = (data: any) => {
  return request.post({ url: '/bms/quotation/lock', data })
}
/*
 * 根据分包ID和投标主体查询细则
 */
export const getTempDetail = (params: { subId: number; domain: number }) => {
  return request.get({ url: `/bms/detailed/temp`, params })
}

/*
 * 细则方案更新
 */
export const detailedUpdate = (data: any) => {
  return request.post({ url: '/bms/detailed/update', data })
}

/*
 * 获取服务类型的树结构
 */
export const getTosTree = () => {
  return request.get({ url: `/bms/detailed/tree` })
}

/*
 * 获取服务类型的树结构
 */
export const addeeiltUpdate = (data: any) => {
  return request.post({ url: `/bms/addeeilt/update`, data })
}

/*
 * 批次详情
 */
export const getDetailData = (batchId: number) => {
  return request.get({ url: `/bms/bid/detail/${batchId}` })
}

//获取投标主体列表
export const getOptionDomain = () => {
  return request.get({ url: '/bms/bid/option/domain' })
}

//修改批次信息
export const updateBatch = (data: any) => {
  return request.post({ url: '/bms/bid/update/batch', data })
}

//修改项目信息
export const updateProject = (data: any) => {
  return request.post({ url: '/bms/bid/update/project', data })
}

//修改分包信息
export const updateSub = (data: any) => {
  return request.post({ url: '/bms/bid/update/sub', data })
}

//获取服务类型的树结构
export const getServiceTypeTree = () => {
  return request.get({ url: '/bms/detailed/tree' })
}

/*
 * 批次分分页列表
 */
export const getOptionsData = () => {
  return request.get({ url: '/bms/bid/option/dictionary' })
}
/*
 * 获取轮数的下拉
 */
export const getRoundOptions = (subId) => {
  return request.get({ url: `/bms/bid/option/domain/rounds/${subId}` })
}
