import { ref, onMounted, onBeforeUnmount, nextTick } from 'vue';
import { useDebounceFn } from '@vueuse/core';

export function useElementHeight() {
  const domRef = ref<HTMLElement | null>(null);
  const domHeight = ref(0);

  // 更新元素高度
  // 使用防抖函数优化性能
  const updateHeight = useDebounceFn(() => {
    if (domRef.value) {
      domHeight.value = domRef.value.offsetHeight;
    }
  }, 100);
  // 在组件挂载时获取初始高度并监听窗口大小变化
  onMounted(() => {
    nextTick(() => {
      updateHeight(); // 初始化时获取高度
    });

    // 监听窗口 resize 事件，动态更新高度
    window.addEventListener('resize', updateHeight);
    
  });

  // 在组件销毁前移除监听器，防止内存泄漏
  onBeforeUnmount(() => {
    window.removeEventListener('resize', updateHeight);
  });

  return {
    domRef,
    domHeight,
    updateHeight
  };
}
