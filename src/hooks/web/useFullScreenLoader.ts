// useFullScreenLoader.ts
import { ref, onUnmounted } from 'vue';
import { ElLoading } from 'element-plus';
import type { LoadingOptions } from 'element-plus';

const useFullScreenLoader = () => {
  const loading:any = ref<ReturnType<typeof ElLoading.service> | null | any>(null);

  // 打开加载遮罩
  const openLoader = (options?: LoadingOptions) => {
    loading.value = ElLoading.service({
      lock: true,
      text: '加载中...',
      background: 'rgba(0, 0, 0, 0.7)',
      ...options,
    });
  };

  // 关闭加载遮罩
  const closeLoader = () => {
    if (loading.value) {
      loading.value.close();
      loading.value = null;
    }
  };

  // 封装一个方法，用于在请求成功或失败后关闭加载遮罩
  const withLoader = async <T>(promise: Promise<T>): Promise<T> => {
    openLoader(); // 打开加载遮罩
    try {
      const result = await promise;
      closeLoader();
      return result;
    } catch (error) {
      closeLoader();
      throw error;
    }
  };

  // 组件卸载时关闭加载遮罩
  onUnmounted(() => {
    closeLoader();
  });

  return {
    openLoader,
    closeLoader,
    withLoader, // 返回 withLoader 方法
  };
};

export default useFullScreenLoader;
