import * as FileApi from '@/api/infra/file'
import { UploadRequestOptions } from 'element-plus/es/components/upload/src/upload'
export const useUpload = (url: () => string) => {
  // 重写ElUpload上传方法
  const httpRequest = async (options: UploadRequestOptions) => {
      // 重写 el-upload httpRequest 文件上传成功会走成功的钩子，失败走失败的钩子
      return new Promise((resolve, reject) => {
        FileApi.updateFile(url(),{ file: options.file, ...options.data })
          .then((res) => {
            if (res.code === 0) {
              resolve(res)
            } else {
              reject(res)
            }
          })
          .catch((res) => {
            reject(res)
          })
      })
    }

  return {
    httpRequest,
  }
}


