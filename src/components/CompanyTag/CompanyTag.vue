<template>
  <el-tooltip :content="value" :disabled="!showTooltip">
    <div class="tag-container">
      <template v-for="(label, index) in processedLabels" :key="index">
        <slot :name="`${field}-content`" :label="label" :index="index">
          {{ label }}
<!--          <el-tag>{{ label }}</el-tag>-->
        </slot>
      </template>
    </div>
  </el-tooltip>
</template>

<script setup>
const props = defineProps({
  value: [String, Number],
  field: { type: String, required: true }, // 必须传入
  showTooltip: Boolean
});

const processedLabels = computed(() => {
  return String(props.value || '').split(/\s*,\s*/).filter(Boolean);
});
</script>

<style lang="scss" scoped>
.company-tag {
  margin-right: 4px;
}
:deep(.main-tag-indicator) {
  vertical-align: middle !important;
}
.tag-container {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.company-tag-tooltip {
  width: fit-content;
  display: inline-block;
}
</style>
