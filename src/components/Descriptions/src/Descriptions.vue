<script lang="ts" setup>
import { PropType, computed, ref, unref, nextTick, watch, onMounted } from 'vue'
import dayjs from 'dayjs'
import { useDesign } from '@/hooks/web/useDesign'
import { propTypes } from '@/utils/propTypes'
import { useAppStore } from '@/store/modules/app'
import { DescriptionsSchema } from '@/types/descriptions'

defineOptions({ name: 'Descriptions' })

const appStore = useAppStore()
const mobile = computed(() => appStore.getMobile)
const attrs = useAttrs()
const slots = useSlots()

const props = defineProps({
  title: propTypes.string.def(''),
  message: propTypes.string.def(''),
  collapse: propTypes.bool.def(true),
  columns: propTypes.number.def(1),
  schema: {
    type: Array as PropType<DescriptionsSchema[]>,
    default: () => []
  },
  data: {
    type: Object as PropType<any>,
    default: () => ({})
  }
})

const { getPrefixCls } = useDesign()
const prefixCls = getPrefixCls('descriptions')
const show = ref(true)

// 存储每个字段的溢出状态
const overflowStates = ref<Record<string, boolean>>({})

const getBindValue = computed(() => {
  const delArr: string[] = ['title', 'message', 'collapse', 'schema', 'data', 'class']
  const obj = { ...attrs, ...props }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
})

const getBindItemValue = (item: DescriptionsSchema) => {
  const delArr: string[] = ['field']
  const obj = { ...item }
  for (const key in obj) {
    if (delArr.indexOf(key) !== -1) {
      delete obj[key]
    }
  }
  return obj
}

const toggleClick = () => {
  if (props.collapse) {
    show.value = !unref(show)
  }
}

// 统一空值判断
const isEmptyValue = (value: any) => {
  return value == null || value === '' || value === '-' || value === undefined
}

const getCellContent = (item: DescriptionsSchema) => {
  const value = props.data[item.field];
  if (isEmptyValue(value)) {
    return '-';
  }
  if (item.dateFormat) {
    return dayjs(value).format(item.dateFormat);
  }
  if (item.dictType) {
    return value + '';
  }
  return item.mappedField ? props.data[item.mappedField] : value;
};

// 存储所有内容的 DOM 引用
const contentRefs = ref<Record<string, HTMLElement>>({});

const setContentRef = (el: HTMLElement | null, field: string) => {
  if (el) {
    contentRefs.value[field] = el;
    // 设置引用后立即检查溢出状态
    nextTick(() => checkOverflow(field));
  }
};

// 同步检测溢出状态
const checkOverflow = (field: string) => {
  const el = contentRefs.value[field];
  if (!el) return false;

  const content = el.textContent?.trim();
  if (!content || content === '-' || content.length < 5) {
    overflowStates.value[field] = false;
    return false;
  }

  // 强制布局计算
  const isOverflow = el.scrollWidth > el.clientWidth + 1;
  overflowStates.value[field] = isOverflow;
  return isOverflow;
};

// 批量检查所有字段的溢出状态
const checkAllOverflows = () => {
  Object.keys(contentRefs.value).forEach(checkOverflow);
};

// 监听数据和显示状态变化
watch(() => props.data, () => {
  nextTick(checkAllOverflows);
}, { deep: true });

watch(show, () => {
  nextTick(checkAllOverflows);
});

onMounted(() => {
  nextTick(checkAllOverflows);
});
</script>

<template>
  <div
    :class="[
      prefixCls,
      'bg-[var(--el-color-white)] dark:bg-[var(--el-bg-color)] dark:border-[var(--el-border-color)] dark:border-1px'
    ]"
  >
    <!-- 标题部分保持不变 -->
    <div
      v-if="title"
      :class="[
        `${prefixCls}-header`,
        'h-50px flex justify-between items-center b-b-1 border-solid border-[var(--el-border-color)] px-10px cursor-pointer dark:border-[var(--el-border-color)]'
      ]"
      @click="toggleClick"
    >
      <div :class="[`${prefixCls}-header__title`, 'relative font-18px font-bold ml-10px']">
        <div class="flex items-center">
          {{ title }}
          <ElTooltip v-if="message" :content="message" placement="right">
            <Icon class="ml-5px" icon="ep:warning" />
          </ElTooltip>
        </div>
      </div>
      <Icon v-if="collapse" :icon="show ? 'ep:arrow-down' : 'ep:arrow-up'" />
    </div>

    <ElCollapseTransition>
      <div v-show="show" :class="[`${prefixCls}-content`, 'p-10px']">
        <ElDescriptions
          :column="props.columns"
          :direction="mobile ? 'vertical' : 'horizontal'"
          border
          v-bind="getBindValue"
        >
          <template v-if="slots['extra']" #extra>
            <slot name="extra"></slot>
          </template>
          <ElDescriptionsItem
            v-for="item in schema"
            :key="item.field"
            min-width="80"
            v-bind="getBindItemValue(item)"
          >
            <template #label>
              <slot
                :name="`${item.field}-label`"
                :row="{
                  label: item.label
                }"
              >{{ item.label }}
              </slot>
            </template>

            <template #default>
              <el-tooltip
                :content="getCellContent(item)"
                placement="top"
                :disabled="!overflowStates[item.field]"
              >
                <span
                  :ref="el => setContentRef(el, item.field)"
                  class="cell-content"
                  @mouseenter="checkOverflow(item.field)"
                >
                  <template v-if="isEmptyValue(getCellContent(item))">
                    <span>-</span>
                  </template>
                  <template v-else-if="item.dictType">
                    <DictTag :type="item.dictType" :value="String(getCellContent(item))" />
                  </template>
                  <template v-else-if="item.field === 'companyName' && getCellContent(item) && typeof getCellContent(item) === 'string'">
                    <CompanyTag
                      :value="getCellContent(item)"
                      :field="item.field"
                      :show-tooltip="false"
                    >
                         <!-- 专用插槽（companyName-content） -->
                      <template #companyName-content="{ label, index }">
                        <el-tag class="company-tag" :type="index === 0 ? 'primary' : 'info'">
                          {{ label }}
                          <span v-if="index === 0" class="main-indicator">(主标)</span>
                        </el-tag>
                      </template>
                    </CompanyTag>
                  </template>
                  <template v-else>
                    {{ getCellContent(item) }}
                  </template>
                </span>
              </el-tooltip>
            </template>
          </ElDescriptionsItem>
        </ElDescriptions>
      </div>
    </ElCollapseTransition>
  </div>
</template>

<style lang="scss" scoped>
$prefix-cls: #{$namespace}-descriptions;
.company-tag {
  margin-right: 4px;
}
.#{$prefix-cls}-header {
  &__title {
    &::after {
      position: absolute;
      top: 3px;
      left: -10px;
      width: 4px;
      height: 70%;
      background: var(--el-color-primary);
      content: '';
    }
  }
}

.#{$prefix-cls}-content {
  :deep(.#{$elNamespace}-descriptions__cell) {
    width: 0;
  }

  .el-descriptions__label {
    vertical-align: middle;
  }

  .cell-content {
    display: inline-block;
    max-width: 65%;
    min-width: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
    box-sizing: border-box;
    line-height: 1.5;
    padding-bottom: 4px;

    // 深度样式穿透
    :deep(*) {
      display: inline !important;
      white-space: nowrap !important;
      max-width: 100% !important;
      text-overflow: ellipsis !important;
      //vertical-align: middle !important;
    }

    // 特别处理标签类组件
    :deep(.el-tag),
    :deep(.company-tag) {
      max-width: 100%;
      display: inline-flex !important;
      vertical-align: middle !important;
    }
  }

  :deep(.#{$elNamespace}-descriptions__table) {
    table-layout: fixed;
    width: 100%;
  }

  :deep(.#{$elNamespace}-descriptions__label) {
    width: 120px;
    text-align: right;
    flex-shrink: 0;
  }

  :deep(.#{$elNamespace}-descriptions__content) {
    flex: 1;
    min-width: 0;
  }
}
</style>
