@font-face {
  font-family: "iconfont"; /* Project id 4980120 */
  src: url('iconfont.woff2?t=1753081139201') format('woff2'),
       url('iconfont.woff?t=1753081139201') format('woff'),
       url('iconfont.ttf?t=1753081139201') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-notActive-circle:before {
  content: "\e61a";
}

.icon-active-circle:before {
  content: "\e600";
}

